import { CcCommonSearchRecommendGroup } from './../../entity/forum/CcCommonSearchRecommendGroup';
import { CcCommonSearchRecommend } from './../../entity/forum/CcCommonSearchRecommend';
import {
  Config,
  Inject,
  Provide,
  Task,
  FORMAT,
  Logger,
  App,
  InjectClient,
} from '@midwayjs/decorator';
import { BaseService, CoolCache } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import { Utils } from '../../../../comm/utils';
import { CoolElasticSearch } from '@cool-midway/es';
import { ILogger } from '@midwayjs/logger';
import * as _ from 'lodash';
import * as phpunserialize from 'phpunserialize';
import { CcForumThread } from '../../entity/forum/CcForumThread';
import { CcForumPost } from '../../entity/forum/CcForumPost';
import { BaseSysConfService } from '../sys/conf';
import { CcForumThreadTrigger } from '../../entity/forum/CcForumThreadTrigger';
import { CcForumPostTrigger } from '../../entity/forum/CcForumPostTrigger';
import { CcCommonAdvertisement } from '../../entity/forum/CcCommonAdvertisement';
import { CcForumThreadWeight } from '../../entity/forum/CcForumThreadWeight';
import { CcCommonMember } from '../../entity/forum/CcCommonMember';
import { EmailService } from './email';
import { Application } from '@midwayjs/koa';
import { CommonSensitiveEntity } from '../../entity/common/sensitive';
import { CommonForumSensitiveEntity } from '../../entity/common/forumSensitive';
import { CcCommonMemberCrime } from '../../entity/forum/CcCommonMemberCrime';
import { CcCommonMemberFieldForum } from '../../entity/forum/CcCommonMemberFieldForum';
import { CcCommonMemberProfile } from '../../entity/forum/CcCommonMemberProfile';
import { CcHomeNotification } from '../../entity/forum/CcHomeNotification';
import { CcForumPostTableid } from '../../entity/forum/CcForumPostTableid';
import { CcForumThreadpartake } from '../../entity/forum/CcForumThreadpartake';
import { TblEvent } from '../../entity/event/TblEvent';
import { IwmsClass } from '../../entity/www/IwmsClass';
import { IwmsNews } from '../../entity/www/IwmsNews';
import { encode } from 'html-entities';
import axios from 'axios';
import { TblEventUser } from '../../entity/event/TblEventUser';
import { TblSystemSettings } from '../../entity/event/TblSystemSettings';
const RPCClient = require("@alicloud/pop-core");
const OSS = require('ali-oss');
const { v4: uuidv4 } = require('uuid');
import * as xlsx from 'node-xlsx';
import { MoreThan } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import { PortalArticleEntity } from '../../entity/portal/Article';
import { TblSchoolDic } from '../../entity/event/TblSchoolDic';

/**
 * 论坛
 */
@Provide()
export class CommonForumService extends BaseService {
  @InjectEntityModel(CcCommonMember, 'forum')
  ccCommonMember: Repository<CcCommonMember>;

  @InjectEntityModel(CcForumThread, 'forum')
  ccForumThread: Repository<CcForumThread>;

  @InjectEntityModel(CcForumPost, 'forum')
  ccForumPost: Repository<CcForumPost>;

  @InjectEntityModel(CcForumThreadpartake, 'forum')
  ccForumThreadpartake: Repository<CcForumThreadpartake>;

  @InjectEntityModel(CcForumPostTableid, 'forum')
  ccForumPostTableid: Repository<CcForumPostTableid>;

  @InjectEntityModel(CcCommonSearchRecommend, 'forum')
  ccCommonSearchRecommend: Repository<CcCommonSearchRecommend>;

  @InjectEntityModel(CcCommonSearchRecommendGroup, 'forum')
  ccCommonSearchRecommendGroup: Repository<CcCommonSearchRecommendGroup>;

  @InjectEntityModel(CcForumThreadTrigger, 'forum')
  CcForumThreadTrigger: Repository<CcForumThreadTrigger>;

  @InjectEntityModel(CcForumPostTrigger, 'forum')
  CcForumPostTrigger: Repository<CcForumPostTrigger>;

  @InjectEntityModel(CcCommonAdvertisement, 'forum')
  ccCommonAdvertisement: Repository<CcCommonAdvertisement>;

  @InjectEntityModel(CcForumThreadWeight, 'forum')
  ccForumThreadWeight: Repository<CcForumThreadWeight>;

  @InjectEntityModel(CcCommonMemberCrime, 'forum')
  ccCommonMemberCrime: Repository<CcCommonMemberCrime>;

  @InjectEntityModel(CcCommonMemberProfile, 'forum')
  ccCommonMemberProfile: Repository<CcCommonMemberProfile>;

  @InjectEntityModel(CcCommonMemberFieldForum, 'forum')
  ccCommonMemberFieldForum: Repository<CcCommonMemberFieldForum>;

  @InjectEntityModel(CcHomeNotification, 'forum')
  ccHomeNotification: Repository<CcHomeNotification>;

  @InjectEntityModel(CommonSensitiveEntity)
  commonSensitiveEntity: Repository<CommonSensitiveEntity>;

  @InjectEntityModel(CommonForumSensitiveEntity)
  commonForumSensitiveEntity: Repository<CommonForumSensitiveEntity>;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @InjectEntityModel(TblEvent, 'id')
  tblEvent: Repository<TblEvent>;

  @InjectEntityModel(TblEventUser, 'id')
  tblEventUser: Repository<TblEventUser>;

  @InjectEntityModel(TblSystemSettings, 'id')
  tblSystemSettings: Repository<TblSystemSettings>;

  @InjectEntityModel(TblSchoolDic, 'id')
  tblSchoolDic: Repository<TblSchoolDic>;

  @InjectEntityModel(IwmsClass, 'www')
  iwmsClass: Repository<IwmsClass>;

  @InjectEntityModel(IwmsNews, 'www')
  iwmsNews: Repository<IwmsNews>;

  @Inject()
  baseSysConfService: BaseSysConfService;

  @Inject()
  emailService: EmailService;

  @Logger()
  logger: ILogger;

  @App()
  app: Application;

  @Inject()
  es: CoolElasticSearch;

  @Inject()
  utils: Utils;

  @Inject()
  baseDir;

  @Config('elasticSearch')
  elasticSearch;

  @Config('discuz')
  discuz;

  @Config('oss')
  oss;

  @Config('aliyun')
  aliyun;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  // Redis键名常量
  private readonly REDIS_LATEST_PID_KEY = 'aliyun_scan_latest_pid';

  // 图片检测相关属性
  private tokenDic = new Array();
  private ossClient: any;
  private isVPC = false;

  /**
   * 根据tid列表更新forum_thread表的fid
   * 从forum_post表中找到对应的fid，然后更新forum_thread表
   * @param tidList tid列表字符串，用逗号分隔
   * @returns 更新结果
   */
  async updateThreadFidByTidList(tidList: string) {
    try {
      // 解析tid列表
      const tids = tidList.split(',').map(tid => parseInt(tid.trim(), 10)).filter(tid => !isNaN(tid));
      
      if (tids.length === 0) {
        throw new Error('没有有效的tid');
      }

      this.logger.info(`开始处理 ${tids.length} 个tid的fid更新`);

      // 从forum_post表中查询每个tid对应的fid
      const posts = await this.ccForumPost.query(
        `SELECT DISTINCT tid, fid FROM cc_forum_post 
         WHERE tid IN (${tids.map(() => '?').join(',')})`,
        tids
      );

      this.logger.info(`找到 ${posts.length} 个有效的tid-fid映射`);

      let totalUpdated = 0;
      let totalErrors = 0;

      // 逐个更新forum_thread表
      for (const post of posts) {
        try {
          const result = await this.ccForumThread.update(
            { tid: post.tid },
            { fid: post.fid }
          );

          if (result.affected && result.affected > 0) {
            totalUpdated++;
            this.logger.info(`成功更新 tid=${post.tid}, fid=${post.fid}`);
          } else {
            this.logger.warn(`tid=${post.tid} 在forum_thread表中未找到或未更新`);
          }
        } catch (updateError) {
          totalErrors++;
          this.logger.error(`更新 tid=${post.tid} 时出错: ${updateError.message}`);
        }
      }

      // 检查是否有tid在forum_post中找不到对应记录
      const foundTids = posts.map(p => p.tid);
      const notFoundTids = tids.filter(tid => !foundTids.includes(tid));
      
      if (notFoundTids.length > 0) {
        this.logger.warn(`以下tid在forum_post表中未找到记录: ${notFoundTids.join(', ')}`);
        totalErrors += notFoundTids.length;
      }

      const result = {
        totalProcessed: tids.length,
        totalUpdated,
        totalErrors,
        success: totalUpdated > 0
      };

      this.logger.info(`fid更新完成: 处理${result.totalProcessed}个tid, 成功更新${result.totalUpdated}个, 错误${result.totalErrors}个`);
      
      return result;

    } catch (error) {
      this.logger.error(`更新forum_thread的fid时发生错误: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 解析forum.txt文件，将数据转换成数组并提交到阿里云内容安全服务
   * @returns 处理结果
   */
  async parseForumTxt() {
    const fs = require('fs');
    const path = require('path');
    const filePath = path.join(this.baseDir, 'forum.txt');
    
    try {
      // 读取文件内容
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      
      // 定义正则表达式来匹配每个条目
      // 匹配格式：数字 + § + 任意内容(非贪婪) + ¶
      const regex = /(\d+)§([\s\S]*?)¶/g;
      
      const result = [];
      let match;
      
      // 使用正则表达式提取所有匹配项
      while ((match = regex.exec(fileContent)) !== null) {
        const pid = match[1];
        const content = match[2].trim();
        
        result.push(`${pid}§${content}¶`);
      }
      
      this.logger.info(`解析forum.txt文件成功，共找到 ${result.length} 条记录`);
      
      // 提交到阿里云内容安全服务
      await this.submitToAliyun(result);
      
      return { success: true, count: result.length };
    } catch (error) {
      this.logger.error(`解析forum.txt文件失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 将解析后的数据提交到阿里云内容安全服务
   * @param items 解析后的数据数组
   */
  async submitToAliyun(items: string[]) {
    // 控制QPS为100，即每秒最多处理100个请求
    const RATE_LIMIT_DELAY = 10; // 每个请求间隔10毫秒，确保QPS不超过100
    const BATCH_SIZE = 20; // 每批处理的数量
    
    try {
      // 创建阿里云客户端
      const client = new RPCClient({
        accessKeyId: this.oss.client.accessKeyId,
        accessKeySecret: this.oss.client.accessKeySecret,        
        endpoint: this.aliyun.greenCip.url,
        apiVersion: '2022-03-02',
      });

      // 请求选项
      const requestOption = {
        method: 'POST',
        formatParams: false,
      };
      
      this.logger.info(`开始提交数据到阿里云内容安全服务，共 ${items.length} 条记录`);
      
      // 分批处理数据
      for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);
        this.logger.info(`处理批次 ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(items.length/BATCH_SIZE)}, 包含 ${batch.length} 条记录`);
        
        // 并行处理当前批次
        const promises = batch.map(async (item, index) => {
          try {
            // 构造请求参数
            const scanParam = {
              "Service": "comment_detection_pro",
              "ServiceParameters": JSON.stringify({            
                "content": item
              })
            };
            
            // 添加延迟以控制QPS
            await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY * index));
            
            // 发送请求            
            const response = await client.request('TextModerationPlus', scanParam, requestOption);
            
            // 标准化响应数据
            const normalizedResponse = this.normalizeResponse(response);
            
            // 如果检测到风险，保存到敏感内容表
            if (normalizedResponse.riskLevel && normalizedResponse.riskLevel !== 'none') {              
              await this.saveSensitiveContent(item, response);
            }
          } catch (error) {
            this.logger.error(`处理记录时出错: ${error.message}`);
            if (error.stack) {
              this.logger.error(`错误堆栈: ${error.stack}`);
            }
            return { error: error.message };
          }
        });
        
        // 等待当前批次完成
        await Promise.all(promises);
        
        // 批次之间添加延迟，确保不超过QPS限制
        if (i + BATCH_SIZE < items.length) {
          this.logger.info(`批次处理完成，等待下一批次...`);
          await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY * BATCH_SIZE));
        }
      }
      
      this.logger.info(`所有数据处理完成`);
    } catch (error) {
      this.logger.error(`提交数据到阿里云内容安全服务时发生错误: ${error.message}`);
      if (error.stack) {
        this.logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }
  }

  /**
   * 使用阿里云内容安全服务扫描论坛帖子
   * @param param 扫描参数
   * @returns 扫描结果
   */
  /**
   * 使用阿里云内容安全服务扫描论坛帖子
   * @param param 扫描参数
   * @returns 扫描结果
   */
  async scanByAliyun(param: any) {
    // 配置常量 - 移到类属性或配置文件中更合适
    // const FID = [param.fid]; // 论坛ID列表
    const FID = [8, 7, 13, 36, 16, 14, 61, 34, 22, 24, 25, 99, 103, 96, 78, 101, 100, 104, 102, 19, 68, 28, 27, 39, 76, 40, 48, 81, 82, 80, 86, 87, 88, 89, 90, 91, 92, 93, 69, 70]; // 论坛ID列表        
    const BATCH_SIZE = 500; // 批处理大小
    const MAX_CONTENT_LENGTH = 550; // 单条内容最大长度
    const MAX_COMBINED_LENGTH = 600; // 合并内容最大长度
    const RATE_LIMIT_DELAY = 1000; // 请求间隔时间(毫秒)
    const START_MARK = '§'; // 内容开始标记
    const END_MARK = '¶'; // 内容结束标记
    
    try {
      // 创建阿里云客户端 - 只创建一次
      const client = new RPCClient({
        accessKeyId: this.oss.client.accessKeyId,
        accessKeySecret: this.oss.client.accessKeySecret,        
        endpoint: this.aliyun.greenCip.url,
        apiVersion: '2022-03-02',
      });

      // 请求选项
      const requestOption = {
        method: 'POST',
        formatParams: false,
      };
      
    // 对每个FID单独处理
    for (let i = 0; i < FID.length; i++) {
        const fid = FID[i];
        try {
          this.logger.info(`开始处理论坛ID: ${fid}`);
          
      // 为每个FID使用单独的Redis键
      const redisFidKey = `${this.REDIS_LATEST_PID_KEY}_${fid}`;
          
          // 查询需要扫描的帖子
          const posts = await this.ccForumPost.query(
            'SELECT pid, message FROM cc_forum_post WHERE fid = ? AND authorid NOT IN (1433,637,1) AND invisible = 0 ORDER BY pid ASC',
            [fid]
          );

          this.logger.info(`论坛ID ${fid} 找到 ${posts.length} 条需要扫描的帖子`);
          
          if (posts.length === 0) {
            this.logger.info(`论坛ID ${fid} 没有新帖子需要处理，跳过`);
            continue; // 跳过当前FID，处理下一个
          }

          // 当前FID的处理结果
          const fidResults = [];
          
          // 分批处理当前FID的帖子
          for (let j = 0; j < posts.length; j += BATCH_SIZE) {
            try {
              const batch = posts.slice(j, j + BATCH_SIZE);
              
              // 找出当前批次中的最小和最大pid，用于记录处理进度
              let batchMinPid = Number.MAX_SAFE_INTEGER;
              let batchMaxPid = 0;
              for (const post of batch) {
                const pid = parseInt(post.pid, 10);
                if (pid < batchMinPid) batchMinPid = pid;
                if (pid > batchMaxPid) batchMaxPid = pid;
              }
              
              this.logger.info(`处理批次 ${j/BATCH_SIZE + 1}/${Math.ceil(posts.length/BATCH_SIZE)}, PID范围: ${batchMinPid}-${batchMaxPid}`);
              
              // 将帖子内容分组合并，确保每组不超过API限制
              const combinedBatches = this.prepareBatchesForScan(batch, START_MARK, END_MARK, MAX_CONTENT_LENGTH, MAX_COMBINED_LENGTH);
              
              // 使用Promise.all并发处理批次
              const batchPromises = combinedBatches.map(combinedBatch => 
                this.processBatch(client, combinedBatch, requestOption)
              );
              
              // 等待当前批次完成
              const batchResults = await Promise.all(batchPromises);
              fidResults.push(...batchResults);
              
              // 记录处理进度
              if (batchMaxPid > 0) {
                this.logger.info(`论坛ID ${fid} 当前批次最大pid: ${batchMaxPid}`);
              }
            } catch (batchError) {
              // 记录当前批次处理错误，但继续处理其他批次
              this.logger.error(`处理论坛ID ${fid} 的批次 ${j/BATCH_SIZE + 1} 时发生错误: ${batchError.message}`);
              if (batchError.stack) {
                this.logger.error(`错误堆栈: ${batchError.stack}`);
              }
              
              // 不中断整个处理过程，继续处理下一批
              fidResults.push({
                batchIndex: j/BATCH_SIZE + 1,
                error: batchError.message
              });
            }
            
            // 如果不是最后一批，等待一段时间以确保不超过QPS限制
            if (j + BATCH_SIZE < posts.length) {
              await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
            }
          }
          
          // 找出当前FID所有处理过的帖子中的最大pid
          let fidMaxPid = 0;
          for (const post of posts) {
            const pid = parseInt(post.pid, 10);
            if (pid > fidMaxPid) {
              fidMaxPid = pid;
            }
          }
          
          // 在当前FID扫描完成后，记录最大pid
          if (fidMaxPid > 0) {
            this.logger.info(`论坛ID ${fid} 扫描完成，最终的最大pid: ${fidMaxPid}`);
            await this.midwayCache.set(redisFidKey, fidMaxPid.toString());
          }
          
          this.logger.info(`论坛ID ${fid} 处理完成`);
          
        } catch (fidError) {
          // 记录当前FID处理错误，但继续处理其他FID
          this.logger.error(`处理论坛ID ${fid} 时发生错误: ${fidError.message}`);
          if (fidError.stack) {
            this.logger.error(`错误堆栈: ${fidError.stack}`);
          }
        }
        
        // 在处理不同FID之间添加延迟，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY * 2));
      }
      
      // 添加一个全局最新PID记录，保存所有FID中的最大PID
      const globalMaxPid = await this.getGlobalMaxPid(FID);
      if (globalMaxPid > 0) {
        await this.midwayCache.set(this.REDIS_LATEST_PID_KEY, globalMaxPid.toString());
        this.logger.info(`所有论坛处理完成，保存全局最大PID到Redis: ${globalMaxPid}`);
      }
    } catch (error) {
      this.logger.error(`扫描帖子时发生错误: ${error.message}`);
      if (error.stack) {
        this.logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }
  }
  
  /**
   * 过滤HTML和BBCode标签
   * @param content 需要过滤的内容
   * @returns 过滤后的内容
   */
  private filterHtmlAndBBCode(content: string): string {
    if (!content) return '';
    
    // 使用单一正则表达式过滤HTML标签
    let filtered = content.replace(/<[^>]+>/g, '');
    
    // 使用更高效的方式过滤BBCode标签
    const bbcodeRegex = /\[(\w+)(=[^\]]+)?\](.*?)\[\/\1\]/gs;
    let prevLength = 0;
    
    // 最多处理10次嵌套，避免无限循环
    for (let i = 0; i < 10 && filtered.length !== prevLength; i++) {
      prevLength = filtered.length;
      filtered = filtered.replace(bbcodeRegex, '$3');
    }
    
    // 过滤掉特定内容及其后面的空格
    const comments = [
      "感谢分享！",
      "顶楼主！",
      "看一下！",
      "Mark一下！",
      "同意！",
      "看看",
      "看一下",
      "谢谢",
      "顶",
      "&nbsp;"
    ];
    
    // 创建正则表达式，匹配特定内容及其后面的空格
    const commentRegex = new RegExp(`(${comments.join('|')})\\s*`, 'g');
    filtered = filtered.replace(commentRegex, '');
    
    // 如果过滤后内容只剩下空格，则返回空字符串
    if (filtered.trim() === '') {
      return '';
    }
    
    return filtered;
  }

  /**
   * 准备批量扫描的内容
   * @param batch 帖子批次
   * @param startMark 开始标记
   * @param endMark 结束标记
   * @param maxContentLength 单条内容最大长度
   * @param maxCombinedLength 合并内容最大长度
   * @returns 准备好的批次数组
   */
  private prepareBatchesForScan(batch: any[], startMark: string, endMark: string, maxContentLength: number, maxCombinedLength: number) {
    // 预处理所有帖子内容，对长内容进行分割而不是截断
    const processedPosts = [];

    for (const post of batch) {
      // 处理内容，将subject和message合并
      let content = '';

      // 如果有subject，先添加subject内容
      if (post.subject && post.subject.trim()) {
        content += post.subject.trim();
      }

      // 如果有message，添加message内容
      if (post.message && post.message.trim()) {
        // 如果已经有subject内容，在中间添加分隔符
        if (content) {
          content += ' ';
        }
        content += post.message.trim();
      }

      // 过滤HTML标签和BBCode标签
      content = this.filterHtmlAndBBCode(content);

      // 如果内容为空，跳过这个帖子
      if (!content) {
        continue;
      }

      // 如果内容长度在限制范围内，直接处理
      if (content.length <= maxContentLength) {
        const formattedContent = `\n\n${post.pid}${startMark}${content}${endMark}`;
        processedPosts.push({
          pid: post.pid,
          formattedContent,
          length: formattedContent.length
        });
      } else {
        // 如果内容太长，分割成多个片段，确保所有内容都被审核
        const contentChunks = this.splitLongContent(content, maxContentLength);

        for (let i = 0; i < contentChunks.length; i++) {
          const chunk = contentChunks[i];
          // 使用原始PID，不添加片段序号
          const formattedContent = `\n\n${post.pid}${startMark}${chunk}${endMark}`;

          processedPosts.push({
            pid: post.pid, // 保持原始pid用于结果关联
            formattedContent,
            length: formattedContent.length,
            isChunk: true, // 标记这是一个内容片段
            chunkIndex: i + 1, // 片段序号
            totalChunks: contentChunks.length // 总片段数
          });
        }

        this.logger.info(`帖子 ${post.pid} 内容过长(${content.length}字符)，已分割为 ${contentChunks.length} 个片段`);
      }
    }

    this.logger.info(`预处理完成，共 ${processedPosts.length} 条内容片段（包含分割的长内容）`);

    // 使用贪心算法优化批次分配
    return this.optimizeBatchAllocation(processedPosts, maxCombinedLength);
  }

  /**
   * 分割长内容为多个片段
   * @param content 原始内容
   * @param maxLength 每个片段的最大长度
   * @returns 内容片段数组
   */
  private splitLongContent(content: string, maxLength: number): string[] {
    const chunks = [];
    let currentIndex = 0;

    while (currentIndex < content.length) {
      let endIndex = currentIndex + maxLength;

      // 如果不是最后一个片段，尝试在合适的位置分割（避免在单词中间分割）
      if (endIndex < content.length) {
        // 向前查找最近的空格、换行符或标点符号作为分割点
        const searchStart = Math.max(currentIndex, endIndex - 50); // 在最后50个字符内查找
        let splitIndex = endIndex;

        for (let i = endIndex - 1; i >= searchStart; i--) { // 从endIndex-1开始，确保不超过maxLength
          const char = content[i];
          if (char === ' ' || char === '\n' || char === '\r' ||
              char === '。' || char === '！' || char === '？' ||
              char === '，' || char === '；' || char === '：' ||
              char === '.' || char === '!' || char === '?' ||
              char === ',' || char === ';' || char === ':') {
            splitIndex = i + 1;
            break;
          }
        }

        // 确保分割点不超过maxLength
        if (splitIndex > currentIndex + maxLength) {
          splitIndex = currentIndex + maxLength;
        }

        endIndex = splitIndex;
      }

      const chunk = content.substring(currentIndex, endIndex).trim();
      if (chunk) {
        chunks.push(chunk);
      }

      currentIndex = endIndex;
    }

    return chunks;
  }
  
  /**
   * 优化批次分配算法
   * @param processedPosts 处理过的帖子
   * @param maxCombinedLength 最大合并长度
   * @returns 优化后的批次
   */
  private optimizeBatchAllocation(processedPosts: any[], maxCombinedLength: number) {
    const combinedBatches = [];

    // 按内容长度排序，从大到小
    const sortedPosts = [...processedPosts].sort((a, b) => b.length - a.length);

    // 使用贪心算法分配批次
    while (sortedPosts.length > 0) {
      // 创建新批次，从最大的帖子开始
      const currentBatch = [sortedPosts.shift()];
      let currentLength = currentBatch[0].length;

      // 使用最佳匹配策略填充批次
      // 创建剩余帖子的副本，避免在迭代过程中修改数组
      const remaining = [...sortedPosts];

      // 尝试找到最适合当前批次的帖子
      for (let i = 0; i < remaining.length; i++) {
        if (currentLength + remaining[i].length <= maxCombinedLength) {
          // 找到合适的帖子，添加到当前批次
          currentBatch.push(remaining[i]);
          currentLength += remaining[i].length;

          // 从排序后的帖子列表中移除（需要考虑分割的内容片段）
          const index = sortedPosts.findIndex(p => {
            // 对于分割的内容片段，通过pid和chunkIndex来匹配
            if (remaining[i].isChunk && p.isChunk) {
              return p.pid === remaining[i].pid && p.chunkIndex === remaining[i].chunkIndex;
            }
            return p.pid === remaining[i].pid && !p.isChunk && !remaining[i].isChunk;
          });
          if (index !== -1) {
            sortedPosts.splice(index, 1);
          }
        }
      }

      // 收集批次中的PID信息和分割信息
      const pids = [...new Set(currentBatch.map(item => item.pid))]; // 去重，因为分割的内容可能有相同的原始pid
      const chunkInfo = currentBatch.filter(item => item.isChunk).map(item => ({
        originalPid: item.pid,
        chunkIndex: item.chunkIndex,
        totalChunks: item.totalChunks
      }));

      // 保存当前批次
      combinedBatches.push({
        content: currentBatch.map(item => item.formattedContent).join(''),
        pids: pids,
        chunkInfo: chunkInfo // 包含分割信息
      });
    }

    // 记录批次信息
    this.logger.info(`批次数量: ${combinedBatches.length}`);

    // 计算总分配的内容片段数量
    const totalItems = combinedBatches.reduce((sum, batch) => {
      return sum + batch.pids.length + batch.chunkInfo.length;
    }, 0);
    this.logger.info(`总计分配 ${totalItems} 条内容片段到 ${combinedBatches.length} 个批次中`);

    return combinedBatches;
  }
  
  /**
   * 处理单个批次的扫描
   * @param client 阿里云客户端
   * @param combinedBatch 合并的批次
   * @param requestOption 请求选项
   * @returns 处理结果
   */
  private async processBatch(client: any, combinedBatch: any, requestOption: any) {
    const scanParam = {
      "Service": "comment_detection_pro",
      "ServiceParameters": JSON.stringify({
        "content": combinedBatch.content
      })
    };

    try {
      this.logger.info(`发送批次请求，内容长度: ${combinedBatch.content.length}`);

      const response = await client.request('TextModerationPlus', scanParam, requestOption);

      // 标准化响应数据
      const normalizedResponse = this.normalizeResponse(response);

      // 如果检测到风险，保存到敏感内容表
      if (normalizedResponse.riskLevel && normalizedResponse.riskLevel !== 'none') {
        this.logger.info(`批次中检测到风险内容，风险级别: ${normalizedResponse.riskLevel}`);

        // 保存敏感内容，包含批次信息以便追踪分割的内容片段
        await this.saveSensitiveContentWithBatchInfo(combinedBatch.content, response, combinedBatch);
      } else {
        this.logger.info(`批次中未检测到风险内容`);
      }

      return {
        pids: combinedBatch.pids,
        riskLevel: normalizedResponse.riskLevel,
        response: normalizedResponse,
        chunkInfo: combinedBatch.chunkInfo // 包含分割信息
      };
    } catch (err) {
      this.logger.error(`处理批次时出错: ${err.message}`);
      if (err.stack) {
        this.logger.error(`错误堆栈: ${err.stack}`);
      }
      return {
        pids: combinedBatch.pids,
        error: err.message
      };
    }
  }
  
  /**
   * 标准化阿里云响应
   * @param response 原始响应
   * @returns 标准化后的响应
   */
  private normalizeResponse(response: any) {
    return {
      riskLevel: response.data?.riskLevel || response.Data?.RiskLevel || 'none',
      result: response.data?.result || response.Data?.Result || []
    };
  }
  
  /**
   * 获取所有FID中的全局最大PID
   * @param fids FID数组
   * @returns 全局最大PID
   */
  private async getGlobalMaxPid(fids: number[]): Promise<number> {
    let globalMaxPid = 0;
    
    try {
      // 遍历所有FID，获取每个FID的最大PID
      for (const fid of fids) {
        const redisFidKey = `${this.REDIS_LATEST_PID_KEY}_${fid}`;
        const pidStr = await this.midwayCache.get(redisFidKey);
        
        if (pidStr && typeof pidStr === 'string') {
          const pid = parseInt(pidStr, 10);
          if (pid > globalMaxPid) {
            globalMaxPid = pid;
          }
        }
      }
      
      this.logger.info(`计算得到全局最大PID: ${globalMaxPid}`);
      return globalMaxPid;
    } catch (error) {
      this.logger.error(`获取全局最大PID时出错: ${error.message}`);
      return globalMaxPid; // 出错时返回当前找到的最大值
    }
  }

  /**
   * 保存敏感内容到数据库
   * @param content 扫描的内容
   * @param response 阿里云响应
   */
  private async saveSensitiveContent(content: string, response: any) {
    try {
      // 标准化响应数据
      const normalizedResponse = this.normalizeResponse(response);
      const { riskLevel, result } = normalizedResponse;

      // 如果没有风险，直接返回，不保存
      if (riskLevel === 'none') return;

      // 准备要保存的记录
      const sensitiveRecords = [];

      // 处理有具体结果的情况
      if (result && result.length > 0) {
        // 保存所有有风险的内容，不再限制只保存政治人物敏感内容
        this.logger.info(`发现有风险内容，风险级别: ${riskLevel}`);

        // 创建敏感内容记录
        sensitiveRecords.push({
          content: content, // 保存完整内容，不限制长度
          response: JSON.stringify(response)
        });
      }
      // 处理没有具体结果但有风险级别的情况
      else if (riskLevel !== 'none') {
        this.logger.info(`检测到风险但没有具体结果: riskLevel=${riskLevel}`);

        sensitiveRecords.push({
          content: content, // 保存完整内容，不限制长度
          response: JSON.stringify(response)
        });
      }

      // 批量保存记录
      if (sensitiveRecords.length > 0) {
        const savedRecords = await this.commonForumSensitiveEntity.save(sensitiveRecords);
        this.logger.info(`已保存 ${savedRecords.length} 条敏感内容记录`);
      }
    } catch (error) {
      this.logger.error(`保存敏感内容到数据库时出错: ${error.message}`);
      if (error.stack) {
        this.logger.error(`错误堆栈: ${error.stack}`);
      }
    }
  }

  /**
   * 保存敏感内容到数据库（带批次信息）
   * @param content 扫描的内容
   * @param response 阿里云响应
   * @param batchInfo 批次信息
   */
  private async saveSensitiveContentWithBatchInfo(content: string, response: any, batchInfo: any) {
    try {
      // 标准化响应数据
      const normalizedResponse = this.normalizeResponse(response);
      const { riskLevel, result } = normalizedResponse;

      // 如果没有风险，直接返回，不保存
      if (riskLevel === 'none') return;

      // 准备要保存的记录
      const sensitiveRecords = [];

      // 处理有具体结果的情况
      if (result && result.length > 0) {
        // 保存所有有风险的内容，包含分割信息
        this.logger.info(`发现有风险内容，风险级别: ${riskLevel}`);

        // 创建敏感内容记录，包含批次和分割信息
        const recordData = {
          content: content, // 保存完整批次内容
          response: JSON.stringify(response)
        };

        // 如果有分割信息，添加到响应中
        if (batchInfo.chunkInfo && batchInfo.chunkInfo.length > 0) {
          const responseWithChunkInfo = {
            ...response,
            chunkInfo: batchInfo.chunkInfo,
            originalPids: batchInfo.pids
          };
          recordData.response = JSON.stringify(responseWithChunkInfo);

          this.logger.info(`批次包含 ${batchInfo.chunkInfo.length} 个分割的内容片段，原始PID: ${batchInfo.pids.join(', ')}`);
        }

        sensitiveRecords.push(recordData);
      }
      // 处理没有具体结果但有风险级别的情况
      else if (riskLevel !== 'none') {
        this.logger.info(`检测到风险但没有具体结果: riskLevel=${riskLevel}`);

        const recordData = {
          content: content,
          response: JSON.stringify(response)
        };

        // 如果有分割信息，添加到响应中
        if (batchInfo.chunkInfo && batchInfo.chunkInfo.length > 0) {
          const responseWithChunkInfo = {
            ...response,
            chunkInfo: batchInfo.chunkInfo,
            originalPids: batchInfo.pids
          };
          recordData.response = JSON.stringify(responseWithChunkInfo);
        }

        sensitiveRecords.push(recordData);
      }

      // 批量保存记录
      if (sensitiveRecords.length > 0) {
        const savedRecords = await this.commonForumSensitiveEntity.save(sensitiveRecords);
        this.logger.info(`已保存 ${savedRecords.length} 条敏感内容记录（包含分割信息）`);
      }
    } catch (error) {
      this.logger.error(`保存敏感内容到数据库时出错: ${error.message}`);
      if (error.stack) {
        this.logger.error(`错误堆栈: ${error.stack}`);
      }
    }
  }
  
  /**
   * 通过帖子ID(pid)获取帖子的完整URL路径
   * @param param 包含pid的参数对象
   * @returns 帖子的完整URL路径
   */
  async getThreadUrlByPid(param: any) {
    try {
      // 参数验证
      if (!param || !param.pid) {
        throw new Error('缺少必要参数pid');
      }
      
      // 查询帖子信息，获取tid和position
      const post = await this.ccForumPost.findOne({
        where: { pid: param.pid }
      });
      
      if (!post) {
        throw new Error(`未找到pid为${param.pid}的帖子`);
      }
      
      // 计算页码，假设每页显示10个帖子
      const postsPerPage = 10;
      const page = Math.ceil(post.position / postsPerPage);
      
      // 构建完整URL
      const baseUrl = 'https://forum.chasedream.com/';
      
      // 如果是主题的第一个帖子(position=1)，直接使用thread-tid-1.html格式
      if (post.position === 1) {
        return {
          url: `${baseUrl}thread-${post.tid}-1-1.html`,
          tid: post.tid,
          fid: post.fid
        };
      } else {
        // 如果不是第一个帖子，使用forum.php?mod=redirect&goto=findpost&pid=xxx格式
        // 这种格式会自动跳转到帖子所在的页面和位置
        return {
          url: `${baseUrl}forum.php?mod=redirect&goto=findpost&pid=${param.pid}`,
          tid: post.tid,
          fid: post.fid,
          page: page
        };
      }
    } catch (error) {
      this.logger.error(`获取帖子URL失败: ${error.message}`);
      throw error;
    }
  }
  

  async updateShowMobile(param: any) {
    await this.tblSystemSettings.update(
      { name: 'show_mobile' },
      {
        value: param.showMobile,
      }
    );
  }

  async getShowMobile(param: any) {
    return await this.tblSystemSettings.findOne({
      where: {
        name: 'show_mobile',
      },
    });
  }

  async appFeatureSwitch(param: any) {
    await this.baseSysConfService.updateVaule(
      'appFeatureSwitch',
      JSON.stringify(param)
    );
  }

  async deleteThread(body) {
    const res = await this.loginForum(body);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    body.formhash = variables.formhash;

    await axios.post(
      this.discuz.deleteThread,
      {
        fid: body.fid,
        formhash: body.formhash,
        handlekey: 'mods',
        listextra: 'page%3D1',
        'moderate[]': body.tid,
        'operations[]': 'delete',
      },
      {
        maxRedirects: 0,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );
  }

  async clearForumCache(cookie) {
    await axios.get(this.discuz.cache),
      {
        headers: {
          Cookie: cookie,
        },
      };
  }

  async stick(param: any) {
    const obj: any = {};
    const event = await this.tblEvent.findOne({
      where: {
        id: param.eventId,
      },
    });

    const user = await this.tblEventUser.findOne({
      where: {
        id: '2',
      },
    });

    obj.stick = param.stick;
    obj.username = user.username;
    obj.password = this.utils.decodeDZString(user.password);

    obj.fid = event.forum_url_fid;
    obj.tid = event.forum_url;

    const res = await this.loginForum(obj);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    obj.formhash = variables.formhash;

    await axios.post(
      `${this.discuz.stick}`,
      {
        formhash: obj.formhash,
        fid: obj.fid,
        listextra: 'page%3D1',
        handlekey: 'mods',
        'moderate[]': obj.tid,
        'operations[]': 'stick',
        sticklevel: obj.stick,
      },
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );

    await this.tblEvent.update(
      {
        id: param.eventId,
      },
      {
        stick: obj.stick,
      }
    );

    await this.clearForumCache(cookies);
  }

  async highlightDigest(param: any) {
    const obj: any = {};
    const event: any = await this.tblEvent.findOneBy({
      id: param.eventId,
    });

    const user = await this.tblEventUser.findOne({
      where: {
        id: '2',
      },
    });

    obj.username = user.username;
    obj.password = this.utils.decodeDZString(user.password);

    obj.fid = event.forum_url_fid;
    obj.tid = event.forum_url;
    obj.digest = param.digest;
    obj.highlight_color = param.highlight_color;
    obj.opt = param.opt;

    const res: any = await this.loginForum(obj);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;

    obj.formhash = variables.formhash;

    let data: any = {
      fid: obj.fid,
      'moderate[]': obj.tid,
      formhash: obj.formhash,
      handlekey: 'mods',
      rdodigest: 'on',
    };

    if (obj.opt === 'highlight') {
      data['operations[]'] = ['highlight'];
      data.highlight_color = obj.highlight_color;
    }

    if (obj.opt === 'digest') {
      data['operations[]'] = ['digest', 'stamp'];

      if (parseInt(obj.digest, 10)) {
        data.digestlevel = 1;
        data.sticklevel = 0;
        data.stamp = 0;
      } else {
        data.digestlevel = 0;
        data.sticklevel = 0;
        data.stamp = '';
      }
    }

    await axios.post(this.discuz.highlightDigest, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookies,
      },
      timeout: 10000,
    });

    let eventModel: any = {};

    if (obj.opt === 'highlight') {
      eventModel.highlight = obj.highlight_color;
    } else if (obj.opt === 'digest') {
      eventModel.digest = obj.digest;
    }

    await this.tblEvent.update({ id: param.eventId }, eventModel);
  }

  async loginForum(user) {
    const result = await axios.post(
      'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=login&loginsubmit=yes&loginfield=auto&submodule=checkpost',
      user,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 10000,
      }
    );

    return result.data;
  }

  async clearWWWCache() {
    const cookie = await this.loginWWW({
      Username: 'Danny',
      Password: '6RW3ME]yp@vi',
    });

    const url = 'https://www.chasedream.com/admin_maintain.aspx';
    let data = {
      ClearCache: true,
    };

    const res = await axios.get(url, {
      headers: {
        Cookie: cookie,
      },
      timeout: 10000,
    });

    const body = res.data.toString();

    let regex =
      /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g;
    let matched = regex.exec(body);
    data['__VIEWSTATE'] = matched[1];

    regex =
      /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g;
    matched = regex.exec(body);
    data['__EVENTVALIDATION'] = matched[1];

    await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: cookie,
      },
      timeout: 10000,
    });
  }

  async loginWWW(user) {
    const login_url = 'https://www.chasedream.com/Sesame-In.aspx';

    const res = await axios.get(login_url);

    const body = res.data.toString();

    let regex =
      /<input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value="(.*?)" \/>/g;
    let matched = regex.exec(body);

    let form = {
      __VIEWSTATE: matched[1],
      Submit: true,
    };

    regex =
      /<input type="hidden" name="__EVENTVALIDATION" id="__EVENTVALIDATION" value="(.*?)" \/>/g;
    matched = regex.exec(body);

    form['__EVENTVALIDATION'] = matched[1];

    const data = Object.assign(form, user);

    const result = await axios.post(login_url, data, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      maxRedirects: 0,
      validateStatus: status => {
        return status >= 200 && status < 400;
      },
    });

    const headers = result.headers;
    let iwmsPass = (headers['set-cookie'] && headers['set-cookie'][1]) || '';

    iwmsPass = iwmsPass.substring(
      iwmsPass.indexOf('=') + 1,
      iwmsPass.indexOf(';')
    );

    return `iwmsUser=${user.Username};iwmsPass=${iwmsPass};iwmsAdmin=y;`;
  }

  async publishToWWW(param: any) {
    const obj = {
      classid: 11,
      memberid: 4,
      author: param.username,
      source: 'ChaseDream论坛',
      sourceUrl: 'https://forum.chasedream.com',
      title: encode(param.subject),
      content: param.content,
      allowRemark: false,
    };

    obj.content = this.utils.replaceLinksForPush(obj.content, 'p');

    const news = await this.iwmsNews.save(obj);
    const articleNum = await this.iwmsNews.count({
      where: {
        classid: news.classid,
      },
    });

    await this.iwmsClass.update(
      {
        classID: news.classid,
      },
      {
        articleNum,
      }
    );

    await this.tblEvent.update(
      { id: param.eventId },
      {
        www_url: news.articleid,
        www_url_senduser: param.uid,
      }
    );

    await this.clearWWWCache();
  }

  async publishToWWW2(param: any) {
    const event = await this.tblEvent.findOneBy({ id: param.eventId });
    const school = await this.tblSchoolDic.findOneBy({ id: event.school_id.toString() });

    const obj = {
      pic: school.logo_url,
      title: param.subject,
      summary: param.summary,
      content: param.content,
      category: 'Events',
      author: param.username,    
      tags: param.tags,
      tagsVal: param.tagsVal,
      majorTags: param.majorTags,
      majorTagsVal: param.majorTagsVal,
      schoolTags: param.schoolTags,
      schoolTagsVal: param.schoolTagsVal,
      datetime: this.utils.now()
    };

    obj.content = this.utils.replaceLinksForPush(obj.content, 'p');
    const res = await this.portalArticleEntity.save(obj);
    
    await this.tblEvent.update(
      { id: param.eventId },
      {
        www_url: res.id,
        www_url_senduser: param.uid,
      }
    );
  }

  async updateThread(param) {
    const res = await this.loginForum(param);

    if (res.Message.messageval !== 'login_succeed')
      throw new Error(res.Message);

    const variables = res.Variables;
    const cookies = `${variables.cookiepre}auth=${encodeURIComponent(
      variables.auth
    )};${variables.cookiepre}saltkey=${variables.saltkey};`;
    param.formhash = variables.formhash;

    let data: any = {
      delattachop: 0,
      formhash: param.formhash,
      message: param.content,
      subject: param.subject,
      tid: param.tid,
      pid: param.pid,
      fid: param.fid,
      typeid: param.typeid,
      usesig: 1,
      wysiwyg: 1,
      htmlon: param.htmlon,
    };

    if (param.hiddenreplies) {
      data.hiddenreplies = 1;
    }

    if (param.ispid) {
      const post = await this.ccForumPost.findOne({
        where: {
          pid: param.pid,
        },
      });

      if (post.position > 1) delete data.subject;
    }

    const result = await axios.post(
      `${this.discuz.apiBaseUrl}&module=editpost&editsubmit=yes`,
      data,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Cookie: cookies,
        },
        timeout: 10000,
      }
    );

    return result.data;
  }

  async publishToForum(param: any) {
    const thread = await this.ccForumThread.save({
      // @ts-ignore
      fid: param.fid,
      posttableid: 0,
      typeid: param.typeid || 0,
      sortid: 0,
      readperm: 0,
      price: 0,
      author: param.username,
      authorid: param.uid,
      subject: param.subject,
      dateline: this.utils.now(),
      lastpost: this.utils.now(),
      lastposter: param.username,
      views: 0,
      replies: 0,
      displayorder: 0,
      highlight: 0,
      digest: 0,
      rate: 0,
      special: 0,
      attachment: 0,
      moderated: 0,
      closed: 0,
      stickreply: 0,
      recommends: 0,
      recommend_add: 0,
      recommend_sub: 0,
      heats: 0,
      status: 32,
      isgroup: 0,
      favtimes: 0,
      sharetimes: 0,
      stamp: -1,
      icon: -1,
      pushedaid: 0,
      cover: 0,
      replycredit: 0,
      relatebytag: 0,
      maxposition: 1,
      bgcolor: '',
      comments: 0,
      hidden: 0,
      oldtypeid: 0,
      showtime: 0,
    });

    await this.ccForumThreadpartake.save({
      // @ts-ignore
      tid: thread.tid,
      uid: param.uid,
      dateline: this.utils.now(),
    });

    const post = await this.ccForumPostTableid.save({});

    const message = this.utils.replaceLinksForPush(param.content, 'f');

    await this.ccForumPost.save({
      // @ts-ignore
      pid: post.pid,
      fid: param.fid,
      // @ts-ignore
      tid: thread.tid,
      first: 1,
      author: param.username,
      authorid: param.uid,
      subject: '',
      dateline: this.utils.now(),
      message,
      useip: '127.0.0.1',
      port: 0,
      invisible: 0,
      anonymous: 0,
      usesig: 1,
      htmlon: 1,
      bbcodeoff: 0,
      smileyoff: -1,
      parseurloff: 0,
      attachment: 0,
      rate: 0,
      ratetimes: 0,
      status: 0,
      tags: 0,
      comment: 0,
      replycredit: 0,
      replytype: 0,
      position: 1,
    });

    await this.tblEvent.update(
      { id: param.eventId },
      {
        // @ts-ignore
        forum_url: thread.tid,
        // @ts-ignore
        forum_url_fid: thread.fid,
        forum_url_pid: post.pid,
        forum_url_typeid: param.typeid || 0,
        forum_url_senduser: param.uid,
      }
    );

    return thread;
  }

  async nav() {
    const groupList = [38, 3, 1, 97, 2, 4, 45, 65, 5];

    const response = await fetch(
      'https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=forumnav',
      {
        method: 'GET',
      }
    );
    const res = await response.json();

    const forums = res.Variables.forums;

    let groups = _.filter(forums, forum => {
      return (
        forum.type === 'group' && groupList.includes(parseInt(forum.fid, 10))
      );
    });

    for (let group of groups) {
      group.forums = _.filter(forums, forum => {
        return forum.type === 'forum' && forum.fup === group.fid;
      });

      delete group.type;
      delete group.fup;
      delete group.status;

      for (let forum of group.forums) {
        delete forum.type;
        delete forum.fup;
        delete forum.status;
        delete forum.viewperm;
        delete forum.postperm;
        delete forum.threadtypes;
      }
    }

    return groups;
  }

  async subnav(param: any) {
    const fid = param.fid;

    const response = await fetch(
      `https://forum.chasedream.com/api/mobile/index.php?mobile=no&version=1&module=forumdisplay&tpp=1&fid=${fid}&page=1`,
      {
        method: 'GET',
      }
    );
    const res = await response.json();

    let threadtypes = [];

    if (res.Variables.threadtypes) {
      for (let [key, value] of Object.entries(
        res.Variables.threadtypes.types
      )) {
        threadtypes.push({
          typeid: key,
          // @ts-ignore
          name: value.replace(/(<([^>]+)>)/gi, ''),
        });
      }
    }

    return threadtypes;
  }

  async hasSensitive(str) {
    const all = (await this.commonSensitiveEntity.find()).map(el => el.text);
    let reg = all.join('|');
    const pattern = new RegExp(reg);

    return pattern.test(str);
  }

  async initSearchRecommandIndex() {
    const index = 'search_recommand';

    try {
      await this.es.client.indices.delete({
        index,
      });
    } catch (err) {
      console.log(err.message);
    }

    await this.es.client.indices.create({
      index,
      mappings: {
        properties: {
          id: {
            type: 'long',
          },
          keyword: {
            type: 'text',
            analyzer: 'ik_max_word',
          },
          html: {
            type: 'keyword',
          },
          status: {
            type: 'boolean',
          },
        },
      },
    });
  }

  async sensitive(param: any) {
    try {
      const censors = await this.nativeQuery(
        'SELECT * FROM forum.cc_common_word',
        [],
        'forum'
      );

      let banned = [];
      let mod = [];
      let replaced = [];

      let bannednum = 0;
      let modnum = 0;
      let replacednum = 0;

      const censorWords = {
        replaced: [],
        banned: [],
        mod: [],
      };

      for (const censor of censors) {
        if (/^\/(.+?)\/$/.test(censor.find)) {
          switch (censor.replacement) {
            case '{BANNED}':
              censorWords.banned.push(censor.find);
              break;
            case '{MOD}':
              censorWords.mod.push(censor.find);
              break;
            default:
              censorWords.replaced.push(censor.find);
              break;
          }
        } else {
          const findRegex = /\\\{(\d+)\\\}/g;
          const find = censor.find.replace(findRegex, '.{0,$1}');

          switch (censor.replacement) {
            case '{BANNED}':
              banned.push(find);
              bannednum++;
              if (bannednum === 1000) {
                censorWords.banned.push(`${banned.join('|')}`);
                banned = [];
                bannednum = 0;
              }
              break;
            case '{MOD}':
              mod.push(find);
              modnum++;
              if (modnum === 1000) {
                censorWords.mod.push(`${mod.join('|')}`);
                mod = [];
                modnum = 0;
              }
              break;
            default:
              replaced.push(find);
              replacednum++;
              if (replacednum === 1000) {
                censorWords.replaced.push(`${replaced.join('|')}`);
                replaced = [];
                replacednum = 0;
              }
              break;
          }
        }
      }

      if (banned.length) {
        censorWords.banned.push(`${banned.join('|')}`);
      }
      if (mod.length) {
        censorWords.mod.push(`${mod.join('|')}`);
      }
      if (replaced.length) {
        censorWords.replaced.push(`${replaced.join('|')}`);
      }

      return this.censorCheck(censorWords, param.message);
    } catch (err) {
      throw new Error(`请求失败: ${err.message}`);
    }
  }

  async sensitiveMonitorGet(param: any) {
    return await this.baseSysConfService.getValue('forumSensitive');
  }

  async sensitiveMonitorUpdate(param: any) {
    await this.baseSysConfService.updateVaule('forumSensitive', param.val);
  }

  censorCheck(censorWords, message) {
    let found = [];
    const bbcodes =
      'b|i|color|size|font|align|list|indent|email|hide|quote|code|free|table|tr|td|img|swf|attach|payto|float';

    if (censorWords.banned.length) {
      for (const banned_word of censorWords.banned) {
        const regex = new RegExp(banned_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '禁止',
            });
          }
        }
      }
    }

    if (censorWords.mod.length) {
      for (const mod_word of censorWords.mod) {
        const regex = new RegExp(mod_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '审核',
            });
          }
        }
      }
    }

    if (censorWords.replaced.length) {
      for (const replaced_word of censorWords.replaced) {
        const regex = new RegExp(replaced_word, 'ig');
        const strippedMessage = message
          .replace(new RegExp(`\\[(${bbcodes})=?.*\\]`, 'ig'), '')
          .replace(new RegExp(`\\[\\/(${bbcodes})\\]`, 'ig'), '');

        const matches = strippedMessage.match(regex);
        if (matches) {
          for (const match of matches) {
            found.push({
              word: match,
              type: '替换',
            });
          }
        }
      }
    }

    return found;
  }

  async initForumIndex() {
    const index = 'forum_post';

    try {
      await this.es.client.indices.delete({
        index,
      });

      await this.baseSysConfService.updateVaule('maxPid', 0);
    } catch (err) {
      console.log(err.message);
    }

    // 创建索引时添加同义词配置
    await this.es.client.indices.create({
      index,
      settings: {
        analysis: {
          filter: {
            school_synonyms: {
              type: "synonym",
              synonyms: [
                "U.S.News, U.S. News, us news, US News",
                // 可以添加更多同义词组
              ]
            }
          },
          analyzer: {
            custom_analyzer: {
              type: "custom",
              tokenizer: "ik_max_word",
              filter: [
                "lowercase",
                "school_synonyms"
              ]
            }
          }
        }
      },
      mappings: {
        properties: {
          fid: {
            type: 'long',
          },
          tid: {
            type: 'long',
          },
          subject: {
            type: 'text',
            analyzer: 'custom_analyzer', // 使用自定义分析器
            search_analyzer: 'custom_analyzer'
          },
          message: {
            type: 'text',
            analyzer: 'custom_analyzer', // 使用自定义分析器
            search_analyzer: 'custom_analyzer'  
          },
          lastpost: {
            type: 'long',
          },
          digest: {
            type: 'short',
          },
          displayorder: {
            type: 'short',
          },
          weight: {
            type: 'short',
          },
        },
      },
    });
  }

  async initForumIndexData() {
    const index = 'forum_post';
    const entityManager = this.CcForumPostTrigger.manager;
    const limitFid = this.discuz.limitFid;

    let maxPid = 0;

    let total = await entityManager.query(
      'SELECT count(*) count FROM forum.cc_forum_post where pid > ? and fid not in(?)',
      [maxPid, limitFid]
    );

    total = Number(total[0].count);
    let page = 0;
    let pageSize = 1000;

    while (true) {
      const posts = await entityManager.query(
        `SELECT a.fid, a.pid, a.tid, a.message, b.subject, b.digest, b.displayorder, b.lastpost FROM forum.cc_forum_post a
              LEFT OUTER JOIN forum.cc_forum_thread b
              on a.tid = b.tid
              where a.pid > ? and a.fid not in(?)
              order by a.pid ASC
              limit ${pageSize}`,
        [maxPid, limitFid]
      );

      try {
        const operations = posts.flatMap(doc => {
          doc.weight = 0;
          return [{ index: { _index: index, _id: doc.pid } }, doc];
        });

        if (posts.length) {
          const res = await this.es.client.bulk({ operations });

          if (res.errors) {
            this.logger.error(JSON.stringify(res.items));
          }
        }
      } catch (err) {
        console.log(err.message);
      } finally {
        if (posts?.length) {
          const pids = posts.map(el => el.pid);
          maxPid = Math.max(...pids);

          await this.baseSysConfService.updateVaule('maxPid', maxPid);
        }
      }

      if ((page === 0 ? 1 : page + 1) * pageSize > total) break;
      else page++;
    }
  }

  async searchRecommendPage(param: any) {
    const { id, gid, name, keyword, content } = param;

    const sql = `
        SELECT a.*, b.name FROM cc_common_search_recommend as a 
        LEFT OUTER JOIN cc_common_search_recommend_group as b 
        on a.gid = b.id 
        WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and a.id=?', id)}
            ${this.setSql(!_.isEmpty(gid), 'and a.gid=?', gid)}
            ${this.setSql(!_.isEmpty(name), 'and b.name like ?', `%${name}%`)}
            ${this.setSql(
              !_.isEmpty(keyword),
              'and a.keyword like ?',
              `%${keyword}%`
            )}
            ${this.setSql(
              !_.isEmpty(content),
              'and a.content like ?',
              `%${content}%`
            )}
            ORDER BY a.id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async searchRecommendAdd(param: any) {
    const index = 'search_recommand';

    const res = await this.ccCommonSearchRecommend.save({
      gid: param.gid,
      keyword: param.keyword,
      content: param.content,
      begin: param.begin || 0,
      end: param.end || 0,
      order: param.order,
      dateline: this.utils.now(),
    });

    await this.es.client.index({
      index,
      id: res.id,
      refresh: true,
      body: {
        content: res.content,
        status: true,
        keyword: param?.keyword
          ?.split('|')
          ?.filter(el => !_.isEmpty(el))
          ?.map(el => decodeURIComponent(el)?.trim()),
      },
    });

    return res;
  }

  async searchRecommendUpdate(param: any) {
    const index = 'search_recommand';

    param.begin = param.begin || 0;
    delete param.name;

    await this.ccCommonSearchRecommend.update(param.id, {
      ...param,
    });

    await this.es.client.update({
      index,
      id: param.id,
      doc: {
        gid: param.gid,
        content: param.content,
        status: !!param.status,
        keyword: param?.keyword
          ?.split('|')
          ?.filter(el => !_.isEmpty(el))
          ?.map(el => decodeURIComponent(el)?.trim()),
      },
    });

    await this.es.client.indices.refresh({ index });
  }

  async searchRecommendDelete(param: any) {
    const index = 'search_recommand';

    await this.ccCommonSearchRecommend.delete({ id: param.id });

    await this.es.client.delete({
      index,
      id: param.id,
    });

    await this.es.client.indices.refresh({ index });
  }

  async searchRecommendGroupList() {
    return await this.ccCommonSearchRecommendGroup.find();
  }

  async searchRecommendGroupPage(param: any) {
    const { id, name } = param;

    const sql = `
        SELECT * FROM cc_common_search_recommend_group WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(id), 'and id=?', id)}
            ${this.setSql(!_.isEmpty(name), 'and name like ?', `%${name}%`)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async searchRecommendGroupAdd(param: any) {
    return await this.ccCommonSearchRecommendGroup.save({
      name: param.name,
      dateline: this.utils.now(),
    });
  }

  async searchRecommendGroupUpdate(param: any) {
    await this.ccCommonSearchRecommendGroup.update(param.id, {
      ...param,
    });
  }

  async searchRecommendGroupDelete(param: any) {
    await this.ccCommonSearchRecommendGroup.delete({ id: param.id });
  }

  async searchRecommend(kw: string) {
    const index = 'search_recommand';

    let keyword = decodeURIComponent(kw?.trim());
    keyword = keyword.replace(/大学|学院/g, '');

    const res = await this.es.client.search({
      index,
      size: 10,
      from: 0,
      query: {
        bool: {
          must: {
            match: {
              keyword,
            },
          },
          filter: {
            bool: {
              must: [{ term: { status: true } }],
            },
          },
        },
      },
    });

    const ids = res?.hits?.hits.map(el => el._id);

    const result = await this.ccCommonSearchRecommend
      .createQueryBuilder('sr')
      .select(['sr.content', 'sr.order'])
      .where('status=1 and (begin = 0 or begin <= :begin) ', {
        begin: this.utils.now(),
      })
      .andWhere('id in (:ids)', {
        ids: !_.isEmpty(ids) ? ids : [null],
      })
      .orderBy('sr.order', 'ASC')
      .getMany();

    return result.map(el => {
      return {
        order: el.order,
        content: el.content,
      };
    });
  }

  async search(qs) {
    const index = 'forum_post';

    const query: any = {
      function_score: {
        query: {
          bool: {
            must: [
              {
                multi_match: {
                  query: decodeURIComponent(qs?.kw?.trim()),
                  type: 'cross_fields',
                  fields: ['subject^10', 'message'],
                  operator: 'and',
                },
              },
            ],
          },
        },
        field_value_factor: {
          field: 'weight',
        },
      },
    };

    const filter = {
      bool: {
        must: [],
      },
    };

    if (!_.isEmpty(qs.digest)) {
      filter.bool.must.push({
        term: {
          digest: qs.digest,
        },
      });
    }

    if (!_.isEmpty(qs.fid)) {
      filter.bool.must.push({
        terms: {
          fid: qs.fid?.split(','),
        },
      });
    }

    if (!_.isEmpty(qs.lastpost)) {
      const opt = parseInt(qs.before, 10) ? 'lte' : 'gte';
      const time = this.utils.now() - parseInt(qs?.lastpost?.trim(), 10);

      filter.bool.must.push({
        range: {
          lastpost: {
            [opt]: time,
          },
        },
      });
    }

    if (filter.bool.must.length) {
      query.function_score.query.bool.filter = filter;
    }

    const res: any = await this.es.client.search({
      index,
      size: 0,
      query,
      aggs: {
        tids: {
          terms: {
            field: 'tid',
            size: 5000,
          },
          aggs: {
            min_pid: {
              min: {
                field: 'pid',
              },
            },
          },
        },
      },
    });

    const output = {
      tids: [],
      pids: [],
    };

    for (const el of res?.aggregations?.tids?.buckets) {
      output.tids.push(el?.key);
      output.pids.push(parseInt(el?.min_pid?.value));
    }

    return output;
  }

  @CoolCache(5)
  async forumAds4WWW() {
    let cdgs = await this.ccCommonAdvertisement
      .createQueryBuilder('sr')
      .select(['sr.parameters', 'sr.type', 'sr.targets'])
      .where(
        'available = 1 and (starttime = 0 or starttime <= :starttime) and (endtime = 0 or endtime >= :endtime) and type in ("headerbanner","float","footerbanner","cornerbanner","couplebanner") ',
        {
          starttime: this.utils.now(),
          endtime: this.utils.now(),
        }
      )
      .orderBy('sr.displayorder', 'DESC')
      .getMany();

    for (let cdg of cdgs) {
      // @ts-ignore
      cdg.parameters = phpunserialize(cdg.parameters);
    }

    return cdgs;
  }

  async threadWeightPage(param: any) {
    const { tid } = param;

    const sql = `
        SELECT * FROM cc_forum_thread_weight WHERE 1 = 1 
            ${this.setSql(!_.isEmpty(tid), 'and tid=?', tid)}
            ORDER BY id DESC`;
    return this.sqlRenderPage(sql, param, false, 'forum');
  }

  async threadWeight(param: any) {
    return await this.ccForumThreadWeight.findOne({
      where: { tid: param.tid },
    });
  }

  async threadWeightAdd(param: any) {
    const exist = await this.ccForumThreadWeight.findOne({
      where: { tid: param.tid },
    });
    if (exist) {
      await this.ccForumThreadWeight.update(exist.id, {
        weight: param.weight || 0,
      });
    } else {
      await this.ccForumThreadWeight.save({
        tid: param.tid,
        weight: param.weight || 0,
        dateline: this.utils.now(),
      });
    }
  }

  async threadWeightDelete(param: any) {
    await this.ccForumThreadWeight.delete({ tid: param.tid });
  }

  async fixForumPostData() {
    let replyMaxPid =
      (await this.baseSysConfService.getValue('replyMaxPid')) || 0;

    let total = await this.nativeQuery(
      'SELECT count(*) count FROM forum.cc_forum_post where pid > ?',
      [replyMaxPid],
      'forum'
    );

    total = Number(total[0].count);
    let page = 0;
    let pageSize = 1000;

    while (true) {
      const posts = await this.nativeQuery(
        `SELECT pid, tid, message FROM forum.cc_forum_post
              where pid > ?
              order by pid ASC
              limit ${pageSize}`,
        [replyMaxPid],
        'forum'
      );

      try {
        const items = [];
        const regex = /(感谢分享|顶楼主|同意|看一下|Mark一下)！( )*/gi;

        for (const post of posts) {
          try {
            regex.lastIndex = 0;
            let message = post.message?.replace(
              /(\[quote\](.*?)\[\/quote\])|[ \n]/gis,
              ''
            );

            const exist = regex.test(message);

            if (exist && message.length <= 10) {
              const replytype = this.utils.replyType(message);
              items.push({
                replytype,
                pid: post.pid,
              });
            }
          } catch (err) {
            console.log(err.message);
          }
        }

        if (items.length) {
          const replytypes = [1, 2, 3, 4, 5];

          for (const replytype of replytypes) {
            const filtered = items.filter(el => el.replytype === replytype);

            if (filtered.length) {
              await this.ccForumPost
                .createQueryBuilder()
                .update()
                .where('pid in (:pids)', { pids: filtered.map(el => el.pid) })
                .set({ replytype })
                .execute();
            }
          }
        }
      } catch (err) {
        console.log(err.message);
      } finally {
        if (posts?.length) {
          const pids = posts.map(el => el.pid);
          replyMaxPid = Math.max(...pids);

          await this.baseSysConfService.updateVaule('replyMaxPid', replyMaxPid);
        }
      }

      if ((page === 0 ? 1 : page + 1) * pageSize > total) break;
      else page++;
    }
  }

  /**
   * 导出敏感内容到Excel文件
   * 查询commonForumSensitiveEntity中response字段包含指定敏感类型的数据
   * 过滤重复数据（根据content字段的唯一标识）
   * 根据敏感类型生成对应的Excel文件
   * @returns 生成的Excel文件信息
   */
  async exportSensitiveContentToExcel() {
    try {
      // 定义需要查询的敏感类型
      const sensitiveTypes = [
        'political_n',
        'political_figure',
        'pornographic_adult',
        'contraband_act',
        'violent_weapons',
        'political_entity',
        'violent_incidents',
        'inappropriate_discrimination',
        'inappropriate_superstition',
        'inappropriate_ethics',
        'contraband_drug',
        'pt_by_recruitment',
        'contraband_gambling',
        'political_p',
        'contraband_entity'
      ];

      const FIDS = [8, 7, 13, 36, 16, 14, 61, 34, 22, 24, 25, 99, 103, 96, 78, 101, 100, 104, 102, 19, 68, 28, 27, 39, 76, 40, 48, 81, 82, 80, 86, 87, 88, 89, 90, 91, 92, 93, 69, 70]; // 论坛ID列表  

      this.logger.info('开始导出敏感内容到Excel文件');

      // 查询所有包含指定敏感类型的记录
      const allRecords = await this.commonForumSensitiveEntity.find({
        where: {
          createTime: MoreThan(new Date('2025-06-19'))
        },                
      });

      this.logger.info(`查询到 ${allRecords.length} 条敏感内容记录`);

      // 按敏感类型分组数据
      const groupedData: { [key: string]: any[] } = {};

      // 初始化分组
      sensitiveTypes.forEach(type => {
        groupedData[type] = [];
      });

      // 用于去重的Set，基于content字段的唯一标识
      const processedIds = new Set<string>();

      // 处理每条记录
      for (const record of allRecords) {
        try {
          // 解析response字段
          const response = JSON.parse(record.response || '{}');

          // 提取content字段的唯一标识（§前面的部分）
          const contentId = this.extractContentId(record.content);

          // 如果已经处理过这个ID，跳过
          if (processedIds.has(contentId)) {
            continue;
          }

          // 检查ccForumPost表中对应的帖子是否invisible不等于0或fid不在FIDS数组中，如果是则过滤掉
          if (contentId && /^\d+$/.test(contentId)) {
            try {
              const forumPost = await this.ccForumPost.findOne({
                where: { pid: parseInt(contentId) },
                select: ['invisible', 'fid']
              });

              if (forumPost) {
                // 将invisible转换为数字进行比较（TypeORM将tinyint映射为boolean，但实际存储的是数字）
                const invisibleValue = Number(forumPost.invisible);

                // 如果invisible不等于0，则跳过
                if (invisibleValue !== 0) {
                  this.logger.debug(`帖子 ${contentId} 的invisible=${invisibleValue}，跳过处理`);
                  continue;
                }

                // 如果fid不在FIDS数组中，则跳过
                if (!FIDS.includes(forumPost.fid)) {
                  this.logger.debug(`帖子 ${contentId} 的fid=${forumPost.fid} 不在允许的FIDS列表中，跳过处理`);
                  continue;
                }
              }
            } catch (dbError) {
              this.logger.warn(`查询帖子 ${contentId} 的invisible和fid状态失败: ${dbError.message}`);
              // 查询失败时继续处理，不跳过
            }
          }

          // 标记为已处理
          processedIds.add(contentId);

          // 检查response中是否包含指定的敏感类型
          const foundTypes = this.findSensitiveTypes(response, sensitiveTypes);

          // 将记录添加到对应的分组中
          foundTypes.forEach(type => {
            groupedData[type].push({
              id: record.id,
              content: record.content,
              contentId: contentId,
              response: record.response,
              createTime: record.createTime,
              updateTime: record.updateTime
            });
          });

        } catch (parseError) {
          this.logger.warn(`解析记录 ${record.id} 的response字段失败: ${parseError.message}`);
        }
      }

      // 生成Excel文件
      const results = [];

      for (const type of sensitiveTypes) {
        const typeData = groupedData[type];

        if (typeData.length === 0) {
          this.logger.info(`敏感类型 ${type} 没有数据，跳过生成Excel文件`);
          continue;
        }

        this.logger.info(`敏感类型 ${type} 有 ${typeData.length} 条数据`);

        // 特殊处理political_n，分成3个文件
        if (type === 'political_n') {
          const files = await this.generatePoliticalNFiles(typeData);
          results.push(...files);
        } else {
          const file = await this.generateSingleExcelFile(type, typeData);
          results.push(file);
        }
      }

      this.logger.info(`Excel文件生成完成，共生成 ${results.length} 个文件`);

      return {
        success: true,
        totalFiles: results.length,
        files: results,
        summary: Object.keys(groupedData).map(type => ({
          type,
          count: groupedData[type].length
        }))
      };

    } catch (error) {
      this.logger.error(`导出敏感内容到Excel文件时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 提取content字段的唯一标识（§前面的部分）
   * @param content 内容字符串
   * @returns 唯一标识
   */
  private extractContentId(content: string): string {
    if (!content) return '';

    const match = content.match(/^(\d+)§/);
    return match ? match[1] : content;
  }

  /**
   * 在response中查找包含的敏感类型
   * @param response 响应对象
   * @param sensitiveTypes 敏感类型列表
   * @returns 找到的敏感类型数组
   */
  private findSensitiveTypes(response: any, sensitiveTypes: string[]): string[] {
    const foundTypes: string[] = [];

    if (!response) return foundTypes;

    // 将response转换为字符串进行搜索
    const responseStr = typeof response === 'string' ? response : JSON.stringify(response);
    const responseStrLower = responseStr.toLowerCase();

    // 检查每个敏感类型是否在response中
    sensitiveTypes.forEach(type => {
      if (responseStrLower.includes(type.toLowerCase())) {
        foundTypes.push(type);
      }
    });

    // 如果没有找到任何敏感类型，但有数据，我们可以记录一下用于调试
    if (foundTypes.length === 0 && responseStr.length > 10) {
      this.logger.debug(`未找到敏感类型，response内容: ${responseStr.substring(0, 200)}...`);
    }

    return foundTypes;
  }

  /**
   * 为political_n类型生成3个Excel文件
   * @param data 数据数组
   * @returns 生成的文件信息数组
   */
  private async generatePoliticalNFiles(data: any[]): Promise<any[]> {
    const files = [];
    const chunkSize = Math.ceil(data.length / 3);

    for (let i = 0; i < 3; i++) {
      const startIndex = i * chunkSize;
      const endIndex = Math.min(startIndex + chunkSize, data.length);
      const chunkData = data.slice(startIndex, endIndex);

      if (chunkData.length > 0) {
        const fileName = `political_n_part_${i + 1}.xlsx`;
        const file = await this.generateExcelFile(fileName, chunkData);
        files.push(file);
      }
    }

    return files;
  }

  /**
   * 生成单个Excel文件
   * @param type 敏感类型
   * @param data 数据数组
   * @returns 生成的文件信息
   */
  private async generateSingleExcelFile(type: string, data: any[]): Promise<any> {
    const fileName = `${type}.xlsx`;
    return await this.generateExcelFile(fileName, data);
  }

  /**
   * 生成Excel文件
   * @param fileName 文件名
   * @param data 数据数组
   * @returns 生成的文件信息
   */
  private async generateExcelFile(fileName: string, data: any[]): Promise<any> {
    try {
      // 准备Excel数据
      const excelData = [];

      // 添加表头
      const headers = [
        'ID',
        'Content ID',
        'Content',
        'Response'
      ];
      excelData.push(headers);

      // 添加数据行
      data.forEach(item => {
        excelData.push([
          item.id,
          item.contentId,
          item.content,
          item.response
        ]);
      });

      // 生成Excel文件缓冲区
      const buffer = xlsx.build([{
        name: fileName.replace('.xlsx', ''),
        data: excelData,
        options: {}
      }]);

      // 创建保存目录
      const saveDir = path.join(this.baseDir, '../upload/sensitive-export');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 生成带时间戳的文件名
      const timestamp = this.utils.today('YYYY-MM-DD_HH-mm-ss');
      const finalFileName = `${timestamp}_${fileName}`;
      const filePath = path.join(saveDir, finalFileName);

      // 保存文件到磁盘
      fs.writeFileSync(filePath, buffer);

      this.logger.info(`Excel文件已保存: ${filePath}, 包含 ${data.length} 条数据`);

      return {
        fileName: finalFileName,
        filePath,
        dataCount: data.length,
        size: buffer.length
      };

    } catch (error) {
      this.logger.error(`生成Excel文件 ${fileName} 时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建OSS客户端
   * @param tokenData 上传token数据
   * @param isVPC 是否使用VPC
   */
  private getOssClient(tokenData: any, isVPC: boolean) {
    if (isVPC) {
      this.ossClient = new OSS({
        accessKeyId: tokenData['AccessKeyId'],
        accessKeySecret: tokenData['AccessKeySecret'],
        stsToken: tokenData['SecurityToken'],
        endpoint: tokenData['OssInternalEndPoint'],
        bucket: tokenData['BucketName'],
      });
    } else {
      this.ossClient = new OSS({
        accessKeyId: tokenData['AccessKeyId'],
        accessKeySecret: tokenData['AccessKeySecret'],
        stsToken: tokenData['SecurityToken'],
        endpoint: tokenData['OssInternetEndPoint'],
        bucket: tokenData['BucketName'],
      });
    }
  }

  /**
   * 递归获取目录下所有图片文件
   * @param dirPath 目录路径
   * @returns 图片文件路径数组
   */
  private getAllImageFiles(dirPath: string): string[] {
    const imageFiles: string[] = [];
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

    const scanDirectory = (currentPath: string) => {
      try {
        const items = fs.readdirSync(currentPath);

        for (const item of items) {
          const fullPath = path.join(currentPath, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            // 递归扫描子目录
            scanDirectory(fullPath);
          } else if (stat.isFile()) {
            // 检查是否为图片文件
            const ext = path.extname(item).toLowerCase();
            if (imageExtensions.includes(ext)) {
              imageFiles.push(fullPath);
            }
          }
        }
      } catch (error) {
        this.logger.error(`扫描目录 ${currentPath} 时出错: ${error.message}`);
      }
    };

    scanDirectory(dirPath);
    return imageFiles;
  }

  /**
   * 上传图片到OSS并获取检测结果
   * @param filePath 本地图片文件路径
   * @param client 阿里云客户端
   * @param requestOption 请求选项
   * @returns 检测结果
   */
  private async uploadAndDetectImage(filePath: string, client: any, requestOption: any) {
    try {
      // 获取上传文件token
      const endpoint = this.aliyun.greenCip.url;
      if (this.tokenDic[endpoint] == null || this.tokenDic[endpoint]['Expiration'] <= Date.parse(new Date().toString()) / 1000) {
        const tokenResponse = await client.request('DescribeUploadToken', '', requestOption);
        this.tokenDic[endpoint] = tokenResponse.Data;
      }

      // 获取上传文件客户端
      this.getOssClient(this.tokenDic[endpoint], this.isVPC);

      const split = filePath.split(".");
      let objectName;
      if (split.length > 1) {
        objectName = this.tokenDic[endpoint].FileNamePrefix + uuidv4() + "." + split[split.length - 1];
      } else {
        objectName = this.tokenDic[endpoint].FileNamePrefix + uuidv4();
      }

      // 上传文件
      const result = await this.ossClient.put(objectName, path.normalize(filePath));

      // 构造检测参数
      const params = {
        "Service": "profilePhotoCheck",
        "ServiceParameters": JSON.stringify({
          "ossBucketName": this.tokenDic[endpoint].BucketName,
          "ossObjectName": objectName
        })
      };

      // 调用接口获取检测结果
      return await client.request('ImageModeration', params, requestOption);
    } catch (error) {
      this.logger.error(`上传并检测图片 ${filePath} 时出错: ${error.message}`);
      throw error;
    }
  }

  /**
   * 扫描forum_avatar目录下的所有图片并进行内容检测
   * @returns 扫描结果
   */
  async scanForumAvatarImages() {
    const RATE_LIMIT_DELAY = 10; // 100 QPS = 10ms间隔

    try {
      this.logger.info('开始扫描forum_avatar目录下的图片文件');

      // 获取forum_avatar目录的绝对路径
      const forumAvatarPath = path.join(process.cwd(), 'forum_avatar');

      if (!fs.existsSync(forumAvatarPath)) {
        throw new Error(`forum_avatar目录不存在: ${forumAvatarPath}`);
      }

      // 递归获取所有图片文件
      const imageFiles = this.getAllImageFiles(forumAvatarPath);
      this.logger.info(`找到 ${imageFiles.length} 个图片文件`);

      if (imageFiles.length === 0) {
        return { success: true, message: '没有找到图片文件', processedCount: 0 };
      }

      // 创建阿里云客户端
      const client = new RPCClient({
        accessKeyId: this.oss.client.accessKeyId,
        accessKeySecret: this.oss.client.accessKeySecret,
        endpoint: this.aliyun.greenCip.url,
        apiVersion: '2022-03-02',
      });

      const requestOption = {
        method: 'POST',
        formatParams: false,
      };

      let processedCount = 0;
      let errorCount = 0;

      // 逐个处理图片文件
      for (let i = 0; i < imageFiles.length; i++) {
        const filePath = imageFiles[i];

        try {
          // 计算相对路径（相对于forum_avatar目录）
          const relativePath = path.relative(forumAvatarPath, filePath);

          this.logger.info(`处理图片 ${i + 1}/${imageFiles.length}: ${relativePath}`);

          // 上传并检测图片
          const response = await this.uploadAndDetectImage(filePath, client, requestOption);

          // 标准化响应数据
          const normalizedResponse = this.normalizeResponse(response);

          // 如果检测到风险，保存到敏感内容表
          if (normalizedResponse.riskLevel && normalizedResponse.riskLevel !== 'none') {
            this.logger.info(`图片 ${relativePath} 检测到风险内容，风险级别: ${normalizedResponse.riskLevel}`);

            // 保存到commonForumSensitiveEntity，content字段保存相对路径
            await this.commonForumSensitiveEntity.save({
              content: relativePath,
              response: JSON.stringify(response)
            });
          } else {
            this.logger.info(`图片 ${relativePath} 未检测到风险内容`);
          }

          processedCount++;

          // 控制QPS，添加延迟
          if (i < imageFiles.length - 1) {
            await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
          }

        } catch (error) {
          errorCount++;
          this.logger.error(`处理图片 ${filePath} 时出错: ${error.message}`);

          // 继续处理下一个图片，不中断整个流程
          continue;
        }
      }

      this.logger.info(`图片扫描完成，共处理 ${processedCount} 个文件，错误 ${errorCount} 个`);

      return {
        success: true,
        totalFiles: imageFiles.length,
        processedCount,
        errorCount,
        message: `扫描完成，共处理 ${processedCount} 个图片文件`
      };

    } catch (error) {
      this.logger.error(`扫描forum_avatar图片时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 读取requestId.txt文件
   * @returns RequestID数组
   */
  private readRequestIds(): string[] {
    try {
      const requestIdPath = path.join(process.cwd(), 'requestId.txt');
      const content = fs.readFileSync(requestIdPath, 'utf-8');
      const requestIds = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      this.logger.info(`读取到 ${requestIds.length} 个 RequestID`);
      return requestIds;
    } catch (error) {
      this.logger.error('读取requestId.txt文件失败:', error);
      throw error;
    }
  }

  /**
   * 查询数据库中匹配的记录
   * @param requestIds RequestID数组
   * @returns 匹配的记录
   */
  private async queryMatchingRecords(requestIds: string[]): Promise<any[]> {
    try {
      // 构建查询条件，使用LIKE查询response字段
      const whereConditions = requestIds.map(() => 'response LIKE ?').join(' OR ');
      const queryParams = requestIds.map(id => `%${id}%`);

      const records = await this.commonForumSensitiveEntity.query(
        `SELECT id, content, response FROM common_forum_sensitive WHERE ${whereConditions}`,
        queryParams
      );

      this.logger.info(`查询到 ${records.length} 条匹配的记录`);
      return records;
    } catch (error) {
      this.logger.error('查询数据库记录失败:', error);
      throw error;
    }
  }

  /**
   * 从content路径提取UID
   * 规则：去掉/和_avatar_big.jpg，然后去掉开头的所有0
   * @param content 内容路径
   * @returns 提取的UID
   */
  private extractUidFromContent(content: string): string {
    try {
      // 移除路径分隔符和文件扩展名
      let uid = content
        .replace(/\//g, '')
        .replace(/_avatar_big\.jpg$/, '');

      // 去掉开头的所有0
      uid = uid.replace(/^0+/, '');

      // 如果全是0，返回'0'
      return uid || '0';
    } catch (error) {
      this.logger.error('提取UID失败:', error);
      return '';
    }
  }

  /**
   * 从response中提取RequestID
   * @param response 响应内容
   * @param requestIds RequestID数组
   * @returns 匹配的RequestID
   */
  private extractRequestIdFromResponse(response: string, requestIds: string[]): string {
    try {
      // 查找匹配的requestId
      for (const requestId of requestIds) {
        if (response.includes(requestId)) {
          return requestId;
        }
      }
      return '';
    } catch (error) {
      this.logger.error('提取RequestID失败:', error);
      return '';
    }
  }

  /**
   * 删除头像文件（big、middle、small三个版本）
   * @param contentPath 内容路径
   * @returns 删除结果
   */
  private deleteAvatarFiles(contentPath: string): { deletedFiles: string[], errors: string[] } {
    const deletedFiles: string[] = [];
    const errors: string[] = [];
    const forumAvatarPath = path.join(process.cwd(), 'forum_avatar');

    try {
      // 构建三个版本的文件路径
      const basePath = contentPath.replace(/_avatar_big\.jpg$/, '');
      const fileVariants = [
        `${basePath}_avatar_big.jpg`,
        `${basePath}_avatar_middle.jpg`,
        `${basePath}_avatar_small.jpg`
      ];

      for (const variant of fileVariants) {
        const fullPath = path.join(forumAvatarPath, variant);

        try {
          if (fs.existsSync(fullPath)) {
            fs.unlinkSync(fullPath);
            deletedFiles.push(variant);
            this.logger.info(`已删除文件: ${variant}`);
          } else {
            this.logger.info(`文件不存在，跳过: ${variant}`);
          }
        } catch (error) {
          const errorMsg = `删除文件失败 ${variant}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }
    } catch (error) {
      const errorMsg = `处理文件路径失败: ${error.message}`;
      errors.push(errorMsg);
      this.logger.error(errorMsg);
    }

    return { deletedFiles, errors };
  }

  /**
   * 删除头像文件的主处理函数
   * @returns 删除结果
   */
  async deleteAvatarFilesByRequestIds(): Promise<Array<{
    uid: string;
    requestId: string;
    contentPath: string;
    deletedFiles: string[];
    errors: string[];
  }>> {
    interface DeleteRecord {
      uid: string;
      requestId: string;
      contentPath: string;
      deletedFiles: string[];
      errors: string[];
    }

    const results: DeleteRecord[] = [];

    try {
      this.logger.info('开始执行头像文件删除任务...');

      // 1. 读取RequestID列表
      const requestIds = this.readRequestIds();

      // 2. 查询匹配的记录
      const records = await this.queryMatchingRecords(requestIds);

      // 3. 处理每条记录
      for (const record of records) {
        const uid = this.extractUidFromContent(record.content);
        const requestId = this.extractRequestIdFromResponse(record.response, requestIds);

        this.logger.info(`\n处理记录: UID=${uid}, RequestID=${requestId}, Content=${record.content}`);

        // 删除文件
        const { deletedFiles, errors } = this.deleteAvatarFiles(record.content);

        results.push({
          uid,
          requestId,
          contentPath: record.content,
          deletedFiles,
          errors
        });
      }

      // 4. 生成报告
      // this.generateDeleteReport(results);

      // 5. 生成CSV文件
      this.generateDeleteCSV(results);

      this.logger.info('头像文件删除任务执行完成!');
      return results;

    } catch (error) {
      this.logger.error('头像文件删除任务执行失败:', error);
      throw error;
    }
  }

  /**
   * 生成删除报告
   * @param results 删除结果
   */
  private generateDeleteReport(results: any[]) {
    const reportLines: string[] = [];
    reportLines.push('=== 头像文件删除报告 ===');
    reportLines.push(`生成时间: ${new Date().toLocaleString()}`);
    reportLines.push(`处理记录数: ${results.length}`);
    reportLines.push('');

    let totalDeletedFiles = 0;
    let totalErrors = 0;

    results.forEach((result, index) => {
      reportLines.push(`记录 ${index + 1}:`);
      reportLines.push(`  UID: ${result.uid}`);
      reportLines.push(`  RequestID: ${result.requestId}`);
      reportLines.push(`  内容路径: ${result.contentPath}`);
      reportLines.push(`  删除的文件 (${result.deletedFiles.length}个):`);

      if (result.deletedFiles.length > 0) {
        result.deletedFiles.forEach((file: string) => {
          reportLines.push(`    - ${file}`);
        });
      } else {
        reportLines.push(`    无文件被删除`);
      }

      if (result.errors.length > 0) {
        reportLines.push(`  错误 (${result.errors.length}个):`);
        result.errors.forEach((error: string) => {
          reportLines.push(`    - ${error}`);
        });
      }

      reportLines.push('');

      totalDeletedFiles += result.deletedFiles.length;
      totalErrors += result.errors.length;
    });

    reportLines.push(`总计删除文件数: ${totalDeletedFiles}`);
    reportLines.push(`总计错误数: ${totalErrors}`);

    // 保存报告到文件
    const reportPath = path.join(process.cwd(), `avatar_delete_report_${Date.now()}.txt`);
    fs.writeFileSync(reportPath, reportLines.join('\n'), 'utf-8');
    this.logger.info(`\n报告已保存到: ${reportPath}`);
  }

  /**
   * 生成删除结果的CSV文件
   * @param results 删除结果
   */
  private generateDeleteCSV(results: any[]) {
    try {
      // 准备CSV数据
      const csvData = [];

      // 添加表头
      const headers = [
        'UID',
        'RequestID',
        'ContentPath',
        'DeletedFilesCount',
        'DeletedFiles',
        'ErrorsCount',
        'Errors'
      ];
      csvData.push(headers);

      // 添加数据行
      results.forEach(result => {
        csvData.push([
          result.uid,
          result.requestId,
          result.contentPath,
          result.deletedFiles.length,
          result.deletedFiles.join('; '), // 用分号分隔多个文件
          result.errors.length,
          result.errors.join('; ') // 用分号分隔多个错误
        ]);
      });

      // 生成Excel文件缓冲区（使用xlsx库生成CSV格式）
      const buffer = xlsx.build([{
        name: 'avatar_delete_results',
        data: csvData,
        options: {}
      }]);

      // 生成带时间戳的文件名
      const timestamp = Date.now();
      const csvFileName = `avatar_delete_results_${timestamp}.xlsx`;
      const csvPath = path.join(process.cwd(), csvFileName);

      // 保存CSV文件到磁盘
      fs.writeFileSync(csvPath, buffer);

      this.logger.info(`CSV文件已保存: ${csvPath}, 包含 ${results.length} 条记录`);

      return {
        fileName: csvFileName,
        filePath: csvPath,
        recordCount: results.length
      };

    } catch (error) {
      this.logger.error(`生成CSV文件时发生错误: ${error.message}`);
      throw error;
    }
  }

  /**
   * 通过PID列表扫描帖子内容并提交到阿里云内容安全服务
   * @param param 包含pids数组的参数对象
   * @returns 扫描结果
   */
  async scanPostsByPids(param: { pids: number[] }) {
    // 参数验证
    if (!param || !param.pids || !Array.isArray(param.pids) || param.pids.length === 0) {
      throw new Error('缺少必要参数pids或pids为空数组');
    }

    // 配置常量
    const BATCH_SIZE = 500; // 批处理大小
    const MAX_CONTENT_LENGTH = 550; // 单条内容最大长度
    const MAX_COMBINED_LENGTH = 600; // 合并内容最大长度
    const RATE_LIMIT_DELAY = 1000; // 请求间隔时间(毫秒)
    const START_MARK = '§'; // 内容开始标记
    const END_MARK = '¶'; // 内容结束标记

    try {
      this.logger.info(`开始扫描指定PID列表的帖子，共 ${param.pids.length} 个PID`);

      // 创建阿里云客户端
      const client = new RPCClient({
        accessKeyId: this.oss.client.accessKeyId,
        accessKeySecret: this.oss.client.accessKeySecret,
        endpoint: this.aliyun.greenCip.url,
        apiVersion: '2022-03-02',
      });

      // 请求选项
      const requestOption = {
        method: 'POST',
        formatParams: false,
      };

      // 根据PID列表查询帖子信息，包含subject字段
      const pidPlaceholders = param.pids.map(() => '?').join(',');
      const posts = await this.ccForumPost.query(
        `SELECT pid, message, fid, subject FROM cc_forum_post WHERE pid IN (${pidPlaceholders}) AND invisible = 0 ORDER BY pid ASC`,
        param.pids
      );

      this.logger.info(`找到 ${posts.length} 条有效的帖子需要扫描`);

      if (posts.length === 0) {
        this.logger.info('没有找到有效的帖子需要处理');
        return { success: true, message: '没有找到有效的帖子', processedCount: 0 };
      }

      const allResults = [];

      // 分批处理帖子
      for (let i = 0; i < posts.length; i += BATCH_SIZE) {
        const batch = posts.slice(i, i + BATCH_SIZE);
        const batchMinPid = Math.min(...batch.map((p: any) => parseInt(p.pid, 10)));
        const batchMaxPid = Math.max(...batch.map((p: any) => parseInt(p.pid, 10)));

        this.logger.info(`处理批次 ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(posts.length/BATCH_SIZE)}, PID范围: ${batchMinPid}-${batchMaxPid}`);

        try {
          // 将帖子内容分组合并，确保每组不超过API限制
          const combinedBatches = this.prepareBatchesForScan(batch, START_MARK, END_MARK, MAX_CONTENT_LENGTH, MAX_COMBINED_LENGTH);

          // 使用Promise.all并发处理批次
          const batchPromises = combinedBatches.map(combinedBatch =>
            this.processBatch(client, combinedBatch, requestOption)
          );

          // 等待当前批次完成
          const batchResults = await Promise.all(batchPromises);
          allResults.push(...batchResults);

        } catch (batchError) {
          // 记录当前批次处理错误，但继续处理其他批次
          this.logger.error(`处理批次 ${Math.floor(i/BATCH_SIZE) + 1} 时发生错误: ${batchError.message}`);
          if (batchError.stack) {
            this.logger.error(`错误堆栈: ${batchError.stack}`);
          }

          // 不中断整个处理过程，继续处理下一批
          allResults.push({
            batchIndex: Math.floor(i/BATCH_SIZE) + 1,
            error: batchError.message
          });
        }

        // 如果不是最后一批，等待一段时间以确保不超过QPS限制
        if (i + BATCH_SIZE < posts.length) {
          await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
        }
      }

      this.logger.info(`PID列表扫描完成，共处理 ${posts.length} 条帖子`);

      return {
        success: true,
        processedCount: posts.length,
        requestedPids: param.pids.length,
        foundPosts: posts.length,
        results: allResults
      };

    } catch (error) {
      this.logger.error(`PID列表扫描失败: ${error.message}`);
      if (error.stack) {
        this.logger.error(`错误堆栈: ${error.stack}`);
      }
      throw error;
    }
  }
}
