import { Inject, Provide, Config, InjectClient } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { In, Repository } from 'typeorm';
import { Utils } from '../../../../comm/utils';
import { PortalArticleEntity } from '../../entity/portal/Article';
import { CoolElasticSearch } from '@cool-midway/es';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';

import * as _ from 'lodash';
import { CommonForumService } from './forum';
import { TblTag } from '../../entity/tag/TblTag';
import { IwmsNews } from '../../entity/www/IwmsNews';
import { IwmsClass } from '../../entity/www/IwmsClass';

/**
 * Portal
 */
@Provide()
export class PortalService extends BaseService {
  @Inject()
  es: CoolElasticSearch;

  @InjectEntityModel(IwmsNews, 'www')
  iwmsNews: Repository<IwmsNews>;

  @InjectEntityModel(IwmsClass, 'www')
  iwmsClass: Repository<IwmsClass>;

  @InjectEntityModel(TblTag, 'id')
  tblTag: Repository<TblTag>;

  @InjectEntityModel(PortalArticleEntity)
  portalArticleEntity: Repository<PortalArticleEntity>;

  @Inject()
  commonForumService: CommonForumService;

  @Inject()
  utils: Utils;

  @Config('redisKey')
  redisKey;

  @InjectClient(CachingFactory, 'default')
  midwayCache: MidwayCache;

  async portalPage(param: any) {
    const { page = 1, pageSize = 50, s } = param;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.portalArticleEntity
      .createQueryBuilder('portal')
      .orderBy('portal.displayorder', 'DESC')
      .addOrderBy('portal.id', 'DESC')
      .skip(skip)
      .take(pageSize);

    if (s) {
      queryBuilder.where('portal.title LIKE :title', { title: `%${s}%` });
    }

    return await queryBuilder.getManyAndCount();
  }

  async portalFindOne(param: any) {
    return await this.portalArticleEntity.findOneBy({
      id: param.id,
    });
  }

  async portalCreate(param: any) {
    param.datetime = this.utils.now();
    
    if (param.forumTid && !param.thread) {
      param.forumTid = this.utils.extractForumTid(param.forumTid);
    } else if(param.thread && (!param.fid || !param.typeid || !param.username || !param.uid)) {
      const thread: any = await this.commonForumService.publishToForum({
        fid: param.fid,
        typeid: param.typeid,
        username: param.username,
        uid: param.uid,
        subject: param.title,
        content: param.content,
      });

      param.forumTid = thread.tid;
    } else {
      param.forumTid = 0;
    }

    // Save to database
    const result = await this.portalArticleEntity.save(param);

    // Sync to Elasticsearch
    await this.es.client.index({
      index: 'portal_articles',
      id: result.id.toString(),
      body: {
        id: result.id,
        title: result.title,
        summary: result.summary,
        forumTid: result.forumTid,
        content: result.content?.replace(/<[^>]+>/g, ''),
        datetime: result.datetime,
        author: result.author,
        tags: result.tags,
        tagsValue: result.tagsValue,
        majorTags: result.majorTags,
        majorTagsValue: result.majorTagsVal,
        schoolTags: result.schoolTags,
        schoolTagsValue: result.schoolTagsVal,
        type: result.type,
        externalLink: result.externalLink
      }
    });

    return result;
  }

  async portalUpdate(param: any) {
    if (param.forumTid && !param.thread) {
      param.forumTid = this.utils.extractForumTid(param.forumTid);
    } else if(param.thread && (!param.fid || !param.typeid || !param.username || !param.uid)) {
      const thread: any = await this.commonForumService.publishToForum({
        fid: param.fid,
        typeid: param.typeid,
        username: param.username,
        uid: param.uid,
        subject: param.title,
        content: param.content,
      });

      param.forumTid = thread.tid;
    } else {
      param.forumTid = 0;
    }

    if(!param.isExternalLink) param.externalLink = '';
    
    delete param.isExternalLink;
    delete param.fid;
    delete param.typeid;
    delete param.uid;
    delete param.username;
    delete param.thread;

    // Update database
    await this.portalArticleEntity.update(param.id, {
      ...param,
    });

    // Update Elasticsearch
    await this.es.client.update({
      index: 'portal_articles',
      id: param.id.toString(),
      body: {
        doc: {
          title: param.title,
          summary: param.summary,
          content: param.content?.replace(/<[^>]+>/g, ''),
          datetime: param.datetime,
          forumTid: param.forumTid,
          author: param.author,
          tags: param.tags,
          tagsValue: param.tagsVal,
          majorTags: param.majorTags,
          majorTagsValue: param.majorTagsVal,
          schoolTags: param.schoolTags,
          schoolTagsValue: param.schoolTagsVal,
          type: param.type,
          externalLink: param.externalLink
        }
      }
    });
  }

  async portalDelete(param: any) {
    // Delete from database
    await this.portalArticleEntity.delete({ id: param.id });
    
    // Delete from Elasticsearch
    await this.es.client.delete({
      index: 'portal_articles',
      id: param.id.toString()
    })
  }

  async transferData() {
      // Clear the table before starting
      await this.portalArticleEntity.clear();
      // Disable auto-increment
      await this.portalArticleEntity.query('ALTER TABLE portal_article AUTO_INCREMENT = 1');
      await this.portalArticleEntity.query('SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO"');

      try {
          const cidArray = [
              5, 14, 15, 16, 17, 18, 19, 20, 21, 22, // MBA
              6, 23, 24, 25, 26, 27, 28, 29,         // Master
              45, 46, 47, 48, 49, 51, 50,            // PhD
              7, 30, 31, 32, 33, 34, 35, 36,         // VISA
              8, 37, 38, 39, 40, 42, 43, 77          // GMAT
          ];

          const iwmsNews = await this.iwmsNews.find({ where: { classid: In(cidArray) } });

          const batchSize = 1000;
          const articles = [];

          for (const item of iwmsNews) {
              const classPath = await this.getFullClassPath(item.classid);
              let tag = await this.getTagFromCategories(classPath);

              const { cleanContent, tid } = item.content
                  ? this.processContentAndExtractTid(item.content)
                  : { cleanContent: '', tid: 0 };

              const article = {
                  pic: 'https://mat.chasedream.com/chasedream/test/images/400x240/1.png',
                  id: item.articleid,
                  title: item.title || '',
                  content: cleanContent,
                  datetime: item.dateandtime ? this.utils.dateToTimeStamp(item.dateandtime) : 0,
                  author: item.author || '',
                  hits: item.hits || 0,
                  summary: item.summary || cleanContent.replace(/<[^>]+>/g, '').substring(0, 100),
                  category: (classPath && classPath[0]) || '',
                  tags: tag || '',
                  forumTid: tid
              };

              if (article.tags === '签证|经验|美国') {
                  article.tags = `${article.tags}|${this.utils.timestampToDate(article.datetime, 'YYYY')}`;
              } else if (classPath[1] === '行前准备') {
                  if (item.title.indexOf('体检') !== -1 || item.content.indexOf('体检') !== -1) {
                      article.tags = '签证|体检';
                  } else if (item.title.indexOf('疫苗') !== -1 || item.content.indexOf('疫苗') !== -1) {
                      article.tags = '签证|疫苗';
                  } else if (item.title.indexOf('档案') !== -1 || item.content.indexOf('档案') !== -1) {
                      article.tags = '签证|档案';
                  }
              }

              articles.push(article);

              // When batch size is reached, save to database only
              if (articles.length === batchSize) {
                  await this.portalArticleEntity.save(articles);
                  articles.length = 0;
              }
          }

          // Save any remaining articles to database
          if (articles.length > 0) {
              await this.portalArticleEntity.save(articles);
          }
      } finally {
          // Re-enable auto-increment
          await this.portalArticleEntity.query('SET SQL_MODE = ""');

          await this.portalArticleEntity.query(`UPDATE portal_article 
              SET summary = LEFT(
                  REGEXP_REPLACE(content, '<[^>]+>', ''),
                  20
              ) 
              WHERE (summary = '' OR summary IS NULL);`);

          await this.portalArticleEntity.query(`UPDATE portal_article
              SET tags = CASE 
                  WHEN tags = 'Master|申请|PS|Resume' THEN 'Master|PS|Resume'
                  WHEN tags = 'Master|申请|总结' THEN 'Master|申请总结'
                  WHEN tags = 'Master|申请|推荐信' THEN 'Master|推荐信'
                  WHEN tags = 'Master|申请|答疑' THEN 'Master|答疑'
                  WHEN tags = 'Master|申请|面试|经验' THEN 'Master|面试经验'
                  WHEN tags = 'MBA|申请|Essay' THEN 'MBA|Essay'
                  WHEN tags = 'MBA|申请|Resume' THEN 'MBA|Resume'
                  WHEN tags = 'MBA|申请|总结' THEN 'MBA|申请总结'
                  WHEN tags = 'MBA|申请|推荐信' THEN 'MBA|推荐信'
                  WHEN tags = 'MBA|申请|答疑' THEN 'MBA|答疑'
                  WHEN tags = 'MBA|申请|面试|经验' THEN 'MBA|面试经验'
                  WHEN tags = '博士|在读|经验' THEN '博士|读书生活'
                  WHEN tags = '博士|学校信息' THEN '博士|学校信息'
                  WHEN tags = '博士|申请|总结' THEN '博士|申请总结'
                  WHEN tags = '博士|申请|答疑' THEN '博士|答疑'
                  WHEN tags = '签证|经验|加拿大' THEN '签证|签经'
                  WHEN tags = '签证|经验|欧洲' THEN '签证|签经'
                  WHEN tags = '签证|问题' THEN '签证|签经'
                  ELSE tags
              END
              WHERE tags IN (
                  'Master|申请|PS|Resume',
                  'Master|申请|总结',
                  'Master|申请|推荐信',
                  'Master|申请|答疑',
                  'Master|申请|面试|经验',
                  'MBA|申请|Essay',
                  'MBA|申请|Resume',
                  'MBA|申请|总结',
                  'MBA|申请|推荐信',
                  'MBA|申请|答疑',
                  'MBA|申请|面试|经验',
                  '博士|在读|经验',
                  '博士|学校信息',
                  '博士|申请|总结',
                  '博士|申请|答疑',
                  '签证|经验|加拿大',
                  '签证|经验|欧洲',
                  '签证|问题'
              );`);

          await this.processTagsToTagsVal();
          
          // 初始化Elasticsearch索引并批量导入数据
          await this.initializeElasticsearchIndex();
      }
  }

  /**
   * 初始化Elasticsearch索引并从数据库批量导入数据
   */
  async initializeElasticsearchIndex() {
    // Define ES index name and mapping
    const ES_INDEX = 'portal_articles';

    // Delete existing index if it exists
    const indexExists = await this.es.client.indices.exists({ index: ES_INDEX });
    if (indexExists) {
        await this.es.client.indices.delete({ index: ES_INDEX });
    }

    // Create new index with mapping
    await this.es.client.indices.create({
        index: ES_INDEX,
        body: {
            mappings: {
                properties: {
                    id: { type: 'keyword' },
                    pic: { type: 'keyword' },
                    title: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    summary: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    content: {
                        type: 'text',
                        analyzer: 'ik_max_word',
                    },
                    datetime: { type: 'date' },
                    author: { type: 'keyword' },
                    category: { type: 'keyword' },
                    tags: { type: 'keyword' },
                    tagsValue: { type: 'keyword' },
                    majorTags: { type: 'keyword' },
                    majorTagsValue: { type: 'keyword' },
                    schoolTags: { type: 'keyword' },
                    schoolTagsValue: { type: 'keyword' }
                }
            }
        }
    });

    // Batch index data from database
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
        // Load articles from database in batches
        const articles = await this.portalArticleEntity
            .createQueryBuilder('article')
            .orderBy('article.id', 'ASC')
            .skip(offset)
            .take(batchSize)
            .getMany();

        if (articles.length === 0) {
            hasMore = false;
            break;
        }

        // Prepare ES documents
        const esDocuments = articles.map(article => ({
            id: article.id,
            pic: article.pic,
            title: article.title,
            summary: article.summary,
            content: article.content?.replace(/<[^>]+>/g, ''), // Remove HTML tags
            datetime: article.datetime,
            author: article.author,
            tags: article.tags,
            tagsValue: article.tagsVal,
            majorTags: article.majorTags,
            majorTagsValue: article.majorTagsVal,
            schoolTags: article.schoolTags,
            schoolTagsValue: article.schoolTagsVal,
            category: article.category,
            forumTid: article.forumTid
        }));

        // Bulk index to Elasticsearch
        await this.es.client.bulk({
            body: esDocuments.flatMap(doc => [
                { index: { _index: ES_INDEX, _id: doc.id } },
                doc
            ])
        });

        offset += batchSize;
        
        // If we got less than batch size, we're done
        if (articles.length < batchSize) {
            hasMore = false;
        }
    }
  }

  async getFullClassPath(classId: number): Promise<string[]> {
        const classInfo = await this.iwmsClass.findOne({
            where: { classID: classId }
        });

        if (!classInfo || !classInfo.class) {
            return [];
        }

        if (!classInfo.parentID) {
            return [classInfo.class];
        }

        const parentPath = await this.getFullClassPath(classInfo.parentID);
        return [...parentPath, classInfo.class];
    }

    async getTagFromCategories(classPath: string[]): Promise<string | null> {
        if (classPath.length < 2) return null;

        const parentCategory = classPath[0];
        const childCategory = classPath[1];

        const categories = {
            MBA: {
                "学校信息": "MBA|学校信息",
                "MBA排名": "MBA|排名",
                "申请FAQ": "MBA|申请|答疑",
                "申请策略": "MBA|申请|总结",
                "Resume": "MBA|申请|Resume",
                "推荐信": "MBA|申请|推荐信",
                "Essay写作": "MBA|申请|Essay",
                "MBA面试": "MBA|申请|面试|经验",
                "申请总结": "MBA|申请|总结"
            },
            Master: {
                "学校介绍": "Master|学校信息",
                "专业与排名": "Master|排名",
                "申请FAQ": "Master|申请|答疑",
                "推荐信": "Master|申请|推荐信",
                "PS/Resume": "Master|申请|PS|Resume",
                "商科面试": "Master|申请|面试|经验",
                "申请总结": "Master|申请|总结"
            },
            PhD: {
                "商学院介绍": "博士|学校信息",
                "排名": "博士|排名",
                "申请FAQ": "博士|申请|答疑",
                "推荐信": "博士|申请|推荐信",
                "申请总结": "博士|申请|总结",
                "PhD学习经验": "博士|在读|经验",
                "PHD面试": "博士|申请|面试|经验"
            },
            VISA: {
                "签证准备": "签证|准备",
                "签证问题集": "签证|问题",
                "签证策略": "签证|准备",
                "美国签证经验": "签证|经验|美国",
                "加拿大签证": "签证|经验|加拿大",
                "欧洲签证": "签证|经验|欧洲",
                "行前准备": ""
            },
            GMAT: {
                "GMAT入门": "GMAT",
                "GMAT报考": "GMAT|报名",
                "GMAT心经": "GMAT|考试经验",
                "GMAT数学": "GMAT|Quant",
                "GMAT阅读": "GMAT|RC",
                "GMAT逻辑": "GMAT|CR",
                "GMAT综合推理": "GMAT|DI"
            }
        };

        if (categories[parentCategory] && categories[parentCategory][childCategory]) {
            return categories[parentCategory][childCategory];
        }

        return null;
    }

    private processContentAndExtractTid(content: string): { cleanContent: string; tid: number } {
        // Remove font-size specifications
        let processed = content.replace(/font-size:\s*12px;/gi, '');

        // Find the style tag position using regex
        const stylePattern = /<style>\s*ignore_js_op|<p>-{5,}/i;
        const match = processed.match(stylePattern);
        if (!match) {
            return { cleanContent: processed, tid: 0 };
        }

        const styleIndex = match.index;

        // Extract the removed portion to find tid
        const removedPortion = processed.substring(styleIndex);
        const tidMatch = removedPortion.match(/thread-(\d+)-/);
        const tid = tidMatch ? parseInt(tidMatch[1], 10) : 0;

        // Return cleaned content (everything before style tag) and extracted tid
        return {
            cleanContent: processed.substring(0, styleIndex),
            tid
        };
    }

  /**
   * 根据tags字段处理tagsVal字段
   * 从tblTag表中根据property=3和main=1过滤数据，然后根据tags值找到对应的id保存到tagsVal
   */
  async processTagsToTagsVal() {
    try {
      // 1. 查询所有符合条件的标签
      const validTags = await this.tblTag.find({
        where: {
          property: 3,
          main: true
        }
      });

      // 创建标签名称到ID的映射
      const tagNameToIdMap = new Map<string, string>();
      validTags.forEach(tag => {
        if (tag.name) {
          tagNameToIdMap.set(tag.name.trim(), tag.id.toString());
        }
      });

      // 2. 查询所有有tags的文章
      const articles = await this.portalArticleEntity
        .createQueryBuilder('article')
        .where('article.tags IS NOT NULL AND article.tags != ""')
        .getMany();

      let processedCount = 0;
      const results = [];

      for (const article of articles) {
        if (article.tags) {
          const tagsVal = this.convertTagsToIds(article.tags, tagNameToIdMap);
          await this.portalArticleEntity.update(article.id, { tagsVal });
          processedCount++;
          results.push({
            id: article.id,
            tags: article.tags,
            tagsVal: tagsVal
          });
        }
      }

      return {
        processed: processedCount,
        total: articles.length,
        results: results,
        availableTags: validTags.map(tag => ({ id: tag.id, name: tag.name }))
      };

    } catch (error) {
      throw new Error(`处理标签转换时出错: ${error.message}`);
    }
  }

  /**
   * 将tags字符串转换为对应的ID字符串
   * @param tags 标签字符串，格式：GMAT|考试经验
   * @param tagNameToIdMap 标签名称到ID的映射
   * @returns 转换后的ID字符串，格式：id1|id2
   */
  private convertTagsToIds(tags: string, tagNameToIdMap: Map<string, string>): string {
    if (!tags || !tags.trim()) {
      return '';
    }

    // 按|分割标签
    const tagNames = tags.split('|').map(tag => tag.trim()).filter(tag => tag.length > 0);
    const matchedIds: string[] = [];

    tagNames.forEach(tagName => {
      const tagId = tagNameToIdMap.get(tagName);
      if (tagId) {
        matchedIds.push(tagId);
      }
    });

    return matchedIds.join('|');
  }

  /**
   * 获取所有可用的标签
   */
  async getAvailableTags(param: any) {
    return await this.tblTag.find({
      where: {
        property: param.property,
        main: true
      },
      select: ['id', 'name', 'order']
    });
  }

  /**
   * 获取 portalBannerIndex 的值
   * @returns 返回 portalBannerIndex 的数字值，如果不存在则返回 0
   */
  async getPortalBannerIndex(): Promise<number> {
    try {
      const value = await this.midwayCache.get(this.redisKey.portalBannerIndex);
      if (value === null || value === undefined) {
        return 0;
      }
      const numValue = parseInt(value.toString(), 10);
      return isNaN(numValue) ? 0 : numValue;
    } catch (error) {
      throw new Error(`获取 portalBannerIndex 失败: ${error.message}`);
    }
  }

  /**
   * 设置或更新 portalBannerIndex 的值
   * @param value 要设置的数字值
   * @returns 返回设置后的值
   */
  async setPortalBannerIndex(value: number): Promise<number> {
    try {
      if (!Number.isInteger(value)) {
        throw new Error('portalBannerIndex 必须是整数');
      }

      await this.midwayCache.set(this.redisKey.portalBannerIndex, value.toString());
      return value;
    } catch (error) {
      throw new Error(`设置 portalBannerIndex 失败: ${error.message}`);
    }
  }

  /**
   * 更新文章显示顺序
   * @param param 包含id和displayOrder的对象或对象数组
   */
  async updateDisplayOrder(param: any) {
    // 如果传入的是单个对象，转换为数组
    const items = Array.isArray(param) ? param : [param];

    for (const item of items) {
      if (!item.id) {
        throw new Error('缺少必要的id参数');
      }

      if (item.displayOrder === undefined || item.displayOrder === null) {
        throw new Error('缺少必要的displayOrder参数');
      }

      // 更新数据库
      await this.portalArticleEntity.update(item.id, {
        displayOrder: item.displayOrder,
      });
    }
  }
}
